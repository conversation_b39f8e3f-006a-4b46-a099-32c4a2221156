/**
 * ErrorBoundary Component for WebTruyen Application
 * Catches React component errors and provides fallback UI
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ErrorBoundaryState,
  ErrorBoundaryProps,
  ErrorFallbackProps,
  ComponentError
} from '@/types/errors';
import { createComponentError, logError, getErrorSeverity } from '@/utils/errorHandling';

/**
 * Default Error Fallback Component
 */
function DefaultErrorFallback({ error, resetError, errorId }: ErrorFallbackProps) {
  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleCopyError = () => {
    const errorDetails = {
      id: errorId,
      message: error.message,
      component: error.componentName,
      timestamp: error.timestamp.toISOString(),
      stack: error.componentStack
    };
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-semibold text-red-900 dark:text-red-100">
            Something went wrong
          </CardTitle>
          <CardDescription className="text-red-700 dark:text-red-300">
            An error occurred in the {error.componentName} component
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <Bug className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription className="mt-2 font-mono text-sm">
              {error.message}
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              onClick={resetError}
              variant="default"
              className="flex-1"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button
              onClick={handleReload}
              variant="outline"
              className="flex-1"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Reload Page
            </Button>
          </div>
          
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              onClick={handleGoHome}
              variant="outline"
              className="flex-1"
            >
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
            <Button
              onClick={handleCopyError}
              variant="ghost"
              size="sm"
              className="flex-1"
            >
              <Bug className="mr-2 h-4 w-4" />
              Copy Error
            </Button>
          </div>
          
          <div className="text-center text-xs text-muted-foreground">
            Error ID: {errorId}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Minimal Error Fallback for isolated components
 */
function MinimalErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <Alert variant="destructive" className="m-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Error in {error.componentName}</AlertTitle>
      <AlertDescription className="mt-2">
        {error.message}
        <Button
          onClick={resetError}
          variant="outline"
          size="sm"
          className="ml-2"
        >
          <RefreshCw className="mr-1 h-3 w-3" />
          Retry
        </Button>
      </AlertDescription>
    </Alert>
  );
}

/**
 * ErrorBoundary Class Component
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const componentError = createComponentError(
      error,
      this.getComponentName(),
      'ErrorBoundary'
    );

    // Add React error info to context
    componentError.context = {
      ...componentError.context,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name
    };

    this.setState({ error: componentError });

    // Log the error
    logError(componentError, {
      errorInfo,
      props: this.props
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(componentError);
    }
  }

  private getComponentName(): string {
    // Try to extract component name from the error stack
    const stack = new Error().stack;
    if (stack) {
      const match = stack.match(/at (\w+)/g);
      if (match && match.length > 1) {
        return match[1].replace('at ', '');
      }
    }
    return 'Unknown';
  }

  private resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: null
    });
  };

  private resetErrorWithDelay = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
    
    this.resetTimeoutId = window.setTimeout(() => {
      this.resetError();
    }, 100);
  };

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  render() {
    if (this.state.hasError && this.state.error && this.state.errorId) {
      const FallbackComponent = this.props.fallback || 
        (this.props.isolate ? MinimalErrorFallback : DefaultErrorFallback);

      return (
        <FallbackComponent
          error={this.state.error}
          resetError={this.resetError}
          errorId={this.state.errorId}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * HOC for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * Hook for error boundary functionality in functional components
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<ComponentError | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error, componentName?: string) => {
    const componentError = createComponentError(
      error,
      componentName || 'Unknown',
      'useErrorHandler'
    );
    setError(componentError);
    logError(componentError);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError, error };
}

export default ErrorBoundary;