"use client"

import React from 'react';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';

interface LineHeightControlProps {
  lineHeight: number;
  onChange: (value: number) => void;
  minHeight?: number;
  maxHeight?: number;
  step?: number;
}

const LineHeightControl: React.FC<LineHeightControlProps> = ({
  lineHeight,
  onChange,
  minHeight = 1.2,
  maxHeight = 2.5,
  step = 0.1
}) => {
  const handleSliderChange = (values: number[]) => {
    onChange(values[0]);
  };

  return (
    <div className="flex items-center space-x-3">
      <span className="text-sm text-muted-foreground whitespace-nowrap">Line Height:</span>
      <div className="flex items-center space-x-2 flex-1 max-w-32">
        <Slider
          value={[lineHeight]}
          onValueChange={handleSliderChange}
          min={minHeight}
          max={maxHeight}
          step={step}
          className="flex-1"
        />
        <Badge variant="secondary" className="w-12 text-center">
          {lineHeight.toFixed(1)}
        </Badge>
      </div>
    </div>
  );
};

export default LineHeightControl;