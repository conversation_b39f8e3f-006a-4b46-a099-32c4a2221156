'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Chapter, Story } from '@/types/story';
import { ChevronLeft, Settings, List, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ChapterListModal from './ChapterListModal';
import SwipeGestures from '@/components/ui/SwipeGestures';
import { useChaptersCache } from '@/hooks/useChaptersCache';
import { toast } from 'sonner';
import { AccessibleAnnouncement, SkipToContent } from '@/components/ui/AccessibilityEnhanced';

interface ReaderLayoutProps {
  children: ReactNode;
  story: Story;
  chapter: Chapter;
  storyId: string;
  onSettingsOpen?: () => void;
}

const ReaderLayout: React.FC<ReaderLayoutProps> = ({
  children,
  story,
  chapter,
  storyId,
  onSettingsOpen
}) => {
  const router = useRouter();
  const { chapters } = useChaptersCache(storyId);
  const [isUIVisible, setIsUIVisible] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const lastScrollY = useRef(0);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const uiTimeout = useRef<NodeJS.Timeout | null>(null);

  // Auto-hide UI on scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const scrollDelta = Math.abs(currentScrollY - lastScrollY.current);
      
      // Show scroll to top button when scrolled down
      setShowScrollTop(currentScrollY > 300);
      
      // Only hide UI if scrolling significantly (avoid hiding on small movements)
      if (scrollDelta > 10) {
        if (currentScrollY > lastScrollY.current && currentScrollY > 100) {
          // Scrolling down - hide UI
          setIsUIVisible(false);
        } else if (currentScrollY < lastScrollY.current) {
          // Scrolling up - show UI
          setIsUIVisible(true);
        }
      }
      
      lastScrollY.current = currentScrollY;
      
      // Clear existing timeout
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
      
      // Auto-show UI after scroll stops
      scrollTimeout.current = setTimeout(() => {
        setIsUIVisible(true);
      }, 2000);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
      if (uiTimeout.current) clearTimeout(uiTimeout.current);
    };
  }, []);

  // Show UI on touch/interaction
  const handleShowUI = () => {
    setIsUIVisible(true);
    
    // Clear existing timeout
    if (uiTimeout.current) {
      clearTimeout(uiTimeout.current);
    }
    
    // Auto-hide after 3 seconds of inactivity
    uiTimeout.current = setTimeout(() => {
      setIsUIVisible(false);
    }, 3000);
  };

  // Navigation functions
  const goToPreviousChapter = () => {
    const currentIndex = chapters.findIndex(ch => ch.id === chapter.id);
    const previousChapter = chapters[currentIndex - 1];
    
    if (previousChapter) {
      router.push(`/stories/${storyId}/${previousChapter.chapter_number}`);
      toast.success(`Chapter ${previousChapter.chapter_number}`);
    } else {
      toast.info('This is the first chapter');
    }
  };

  const goToNextChapter = () => {
    const currentIndex = chapters.findIndex(ch => ch.id === chapter.id);
    const nextChapter = chapters[currentIndex + 1];
    
    if (nextChapter) {
      router.push(`/stories/${storyId}/${nextChapter.chapter_number}`);
      toast.success(`Chapter ${nextChapter.chapter_number}`);
    } else {
      toast.info('This is the last chapter');
    }
  };

  const goBackToStory = () => {
    router.push(`/stories/${storyId}`);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Swipe gesture handlers
  const handleSwipeLeft = () => {
    goToNextChapter();
  };

  const handleSwipeRight = () => {
    goToPreviousChapter();
  };

  const handleSwipeUp = () => {
    // Show UI on swipe up
    handleShowUI();
  };

  const handleSwipeDown = () => {
    // Hide UI on swipe down
    setIsUIVisible(false);
  };

  // Calculate reading progress
  const [readingProgress, setReadingProgress] = useState(0);
  
  useEffect(() => {
    const updateProgress = () => {
      const scrolled = window.scrollY;
      const maxScroll = document.body.scrollHeight - window.innerHeight;
      const progress = maxScroll > 0 ? Math.min(100, (scrolled / maxScroll) * 100) : 0;
      setReadingProgress(progress);
    };
    
    window.addEventListener('scroll', updateProgress, { passive: true });
    return () => window.removeEventListener('scroll', updateProgress);
  }, []);

  return (
    <>
      <SkipToContent />
      <AccessibleAnnouncement 
        message={`Reading ${story.title}, Chapter ${chapter.chapter_number}`} 
        priority="polite" 
      />
      
      <SwipeGestures
        onSwipeLeft={handleSwipeLeft}
        onSwipeRight={handleSwipeRight}
        onSwipeUp={handleSwipeUp}
        onSwipeDown={handleSwipeDown}
        threshold={60}
        className="min-h-screen bg-zinc-900 relative"
      >
        {/* Top Navigation Bar */}
        <header 
          className={`fixed top-0 left-0 right-0 z-50 bg-zinc-900/95 backdrop-blur-sm border-b border-zinc-800 transition-transform duration-300 safe-area-padding-top ${
            isUIVisible ? 'translate-y-0' : '-translate-y-full'
          }`}
          role="banner"
          aria-label="Reader navigation"
        >
          <div className="flex items-center justify-between p-3 sm:p-4">
            {/* Back Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={goBackToStory}
              className="flex items-center gap-2 text-gray-300 hover:text-white touch-manipulation min-h-touch-target"
              aria-label={`Go back to ${story.title} story page`}
            >
              <ChevronLeft className="h-4 w-4" aria-hidden="true" />
              <span className="hidden xs:inline">Back</span>
            </Button>

            {/* Story Title - Truncated on mobile */}
            <div className="flex-1 text-center px-2 min-w-0" role="heading" aria-level={1}>
              <h1 className="text-sm sm:text-base font-medium text-white truncate">
                {story.title}
              </h1>
              <p className="text-xs text-gray-400 truncate" aria-label={`Chapter ${chapter.chapter_number}${chapter.title ? `: ${chapter.title}` : ''}`}>
                Chapter {chapter.chapter_number}
                {chapter.title && (
                  <span className="hidden sm:inline">: {chapter.title}</span>
                )}
              </p>
            </div>

            {/* Action Buttons */}
            <nav className="flex items-center gap-1 sm:gap-2" aria-label="Reader controls">
              <ChapterListModal 
                storyId={storyId} 
                currentChapter={chapter}
                className="p-2 sm:p-3"
              />
              
              {onSettingsOpen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onSettingsOpen}
                  className="p-2 sm:p-3 text-gray-300 hover:text-white touch-manipulation min-h-touch-target"
                  aria-label="Open reader settings"
                >
                  <Settings className="h-4 w-4" aria-hidden="true" />
                </Button>
              )}
            </nav>
          </div>
        </header>

        {/* Main Content Area */}
        <main 
          id="main-content"
          className="pt-16 sm:pt-20 pb-safe-area"
          onClick={handleShowUI}
          onTouchStart={handleShowUI}
          role="main"
          aria-label="Chapter content"
          tabIndex={-1}
        >
          {children}
        </main>

        {/* Scroll to Top Button */}
        {showScrollTop && (
          <Button
            variant="secondary"
            size="sm"
            onClick={scrollToTop}
            className={`fixed bottom-4 right-4 z-40 rounded-full p-3 shadow-lg transition-all duration-300 safe-area-padding-bottom ${
              isUIVisible ? 'opacity-100' : 'opacity-60'
            }`}
            aria-label="Scroll to top of chapter"
          >
            <ChevronUp className="h-4 w-4" aria-hidden="true" />
          </Button>
        )}

        {/* Reading Progress Indicator */}
        <div 
          className="fixed top-0 left-0 right-0 z-40"
          role="progressbar"
          aria-valuenow={Math.round(readingProgress)}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-label={`Reading progress: ${Math.round(readingProgress)}%`}
        >
          <div 
            className="h-1 bg-indigo-500 transition-all duration-150"
            style={{ width: `${readingProgress}%` }}
          />
        </div>
      </SwipeGestures>
    </>
  );
};

export default ReaderLayout;