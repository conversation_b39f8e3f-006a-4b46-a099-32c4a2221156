"use client"

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FontSizeControlProps {
  fontSize: number;
  onIncrease: () => void;
  onDecrease: () => void;
  minSize?: number;
  maxSize?: number;
}

const FontSizeControl: React.FC<FontSizeControlProps> = ({
  fontSize,
  onIncrease,
  onDecrease,
  minSize = 12,
  maxSize = 24
}) => {
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-muted-foreground">Font Size:</span>
      <Button
        variant="outline"
        size="sm"
        onClick={onDecrease}
        disabled={fontSize <= minSize}
        className="w-8 h-8 p-0"
      >
        A-
      </Button>
      <Badge variant="secondary" className="w-8 text-center">
        {fontSize}
      </Badge>
      <Button
        variant="outline"
        size="sm"
        onClick={onIncrease}
        disabled={fontSize >= maxSize}
        className="w-8 h-8 p-0"
      >
        A+
      </Button>
    </div>
  );
};

export default FontSizeControl;