<svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="400" fill="#1f2937"/>
  
  <!-- Gradient overlay -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#111827;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  <rect width="300" height="400" fill="url(#grad1)"/>
  
  <!-- Book icon -->
  <g transform="translate(125, 160)">
    <rect x="0" y="0" width="50" height="60" rx="4" fill="#6b7280" stroke="#9ca3af" stroke-width="2"/>
    <rect x="5" y="5" width="40" height="50" rx="2" fill="#374151"/>
    <line x1="10" y1="15" x2="40" y2="15" stroke="#9ca3af" stroke-width="1.5"/>
    <line x1="10" y1="25" x2="35" y2="25" stroke="#9ca3af" stroke-width="1"/>
    <line x1="10" y1="35" x2="40" y2="35" stroke="#9ca3af" stroke-width="1"/>
    <line x1="10" y1="45" x2="30" y2="45" stroke="#9ca3af" stroke-width="1"/>
  </g>
  
  <!-- Text -->
  <text x="150" y="260" text-anchor="middle" fill="#9ca3af" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="500">No Cover Image</text>
  <text x="150" y="280" text-anchor="middle" fill="#6b7280" font-family="system-ui, -apple-system, sans-serif" font-size="12">Available</text>
</svg>