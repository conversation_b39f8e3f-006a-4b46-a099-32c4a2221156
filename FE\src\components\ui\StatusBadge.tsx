'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Download, Sparkles, CheckCircle, Clock, Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

// Base status badge types
type StatusType = 'scraped' | 'enhanced' | 'completed' | 'ongoing' | 'paused' | 'not-scraped';

interface StatusBadgeProps {
  type: StatusType;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

// Status configuration mapping
const statusConfig: Record<StatusType, {
  label: string;
  variant: 'default' | 'secondary' | 'destructive' | 'outline';
  className: string;
  icon?: React.ComponentType<{ className?: string }>;
}> = {
  scraped: {
    label: 'Scraped',
    variant: 'outline',
    className: 'text-green-600 border-green-600 bg-green-50 dark:bg-green-900/20',
    icon: Download
  },
  enhanced: {
    label: 'Enhanced',
    variant: 'outline',
    className: 'text-blue-600 border-blue-600 bg-blue-50 dark:bg-blue-900/20',
    icon: Sparkles
  },
  completed: {
    label: 'Completed',
    variant: 'default',
    className: 'bg-green-500 text-white hover:bg-green-600',
    icon: CheckCircle
  },
  ongoing: {
    label: 'Ongoing',
    variant: 'secondary',
    className: 'bg-blue-500 text-white hover:bg-blue-600',
    icon: Clock
  },
  paused: {
    label: 'Paused',
    variant: 'outline',
    className: 'text-yellow-600 border-yellow-600 bg-yellow-50 dark:bg-yellow-900/20',
    icon: Pause
  },
  'not-scraped': {
    label: 'Not Scraped',
    variant: 'destructive',
    className: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
  }
};

const sizeClasses = {
  sm: 'text-xs px-2 py-0.5',
  md: 'text-sm px-2.5 py-1',
  lg: 'text-base px-3 py-1.5'
};

const iconSizes = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-5 w-5'
};

/**
 * Reusable status badge component with consistent styling and behavior
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  type,
  className,
  showIcon = true,
  size = 'md'
}) => {
  const config = statusConfig[type];
  const Icon = config.icon;

  return (
    <Badge
      variant={config.variant}
      className={cn(
        config.className,
        sizeClasses[size],
        'inline-flex items-center gap-1',
        className
      )}
    >
      {showIcon && Icon && (
        <Icon className={iconSizes[size]} />
      )}
      {config.label}
    </Badge>
  );
};

// Compound component for chapter-specific status badges
interface ChapterStatusBadgesProps {
  isScraped: boolean;
  isEnhanced: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcons?: boolean;
}

export const ChapterStatusBadges: React.FC<ChapterStatusBadgesProps> = ({
  isScraped,
  isEnhanced,
  className,
  size = 'md',
  showIcons = true
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {isEnhanced ? (
        <StatusBadge type="enhanced" size={size} showIcon={showIcons} />
      ) : isScraped ? (
        <StatusBadge type="scraped" size={size} showIcon={showIcons} />
      ) : (
        <StatusBadge type="not-scraped" size={size} showIcon={showIcons} />
      )}
    </div>
  );
};

// Compound component for story status badges
interface StoryStatusBadgeProps {
  status: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

export const StoryStatusBadge: React.FC<StoryStatusBadgeProps> = ({
  status,
  className,
  size = 'md',
  showIcon = true
}) => {
  const getStatusType = (status: string): StatusType => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'completed';
      case 'ongoing':
        return 'ongoing';
      case 'paused':
      default:
        return 'paused';
    }
  };

  return (
    <StatusBadge
      type={getStatusType(status)}
      size={size}
      showIcon={showIcon}
      className={className}
    />
  );
};

// Export individual components and compound patterns
export default StatusBadge;