/**
 * Standardized Error Types for WebTruyen Application
 * Provides type-safe error handling across the application
 */

// Base error interface
export interface BaseError {
  message: string;
  code?: string;
  timestamp: Date;
  context?: Record<string, unknown>;
}

// API Error types
export interface ApiError extends BaseError {
  status: number;
  statusText: string;
  endpoint?: string;
  method?: string;
}

// Application Error types
export interface AppError extends BaseError {
  type: AppErrorType;
  recoverable: boolean;
  userMessage?: string;
}

// Component Error types
export interface ComponentError extends BaseError {
  componentName: string;
  componentStack?: string;
  errorBoundary?: string;
}

// Validation Error types
export interface ValidationError extends BaseError {
  field: string;
  value: unknown;
  constraint: string;
}

// Network Error types
export interface NetworkError extends BaseError {
  url?: string;
  timeout?: boolean;
  offline?: boolean;
}

// Error Type Enums
export enum AppErrorType {
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Error Context types
export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp: Date;
  severity: ErrorSeverity;
  tags?: string[];
}

// Error Handler types
export type ErrorHandler = (error: BaseError) => void;
export type ErrorRecoveryHandler = (error: BaseError) => Promise<boolean>;

// Error Boundary types
export interface ErrorBoundaryState {
  hasError: boolean;
  error: ComponentError | null;
  errorId: string | null;
}

export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: ErrorHandler;
  isolate?: boolean;
}

export interface ErrorFallbackProps {
  error: ComponentError;
  resetError: () => void;
  errorId: string;
}

// Error Response types for API
export interface ErrorResponse {
  error: {
    message: string;
    code?: string;
    details?: Record<string, unknown>;
  };
  timestamp: string;
  path?: string;
  requestId?: string;
}

// Error Reporting types
export interface ErrorReport {
  error: BaseError;
  context: ErrorContext;
  stackTrace?: string;
  breadcrumbs?: ErrorBreadcrumb[];
}

export interface ErrorBreadcrumb {
  timestamp: Date;
  category: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, unknown>;
}

// Utility types
export type ErrorLike = Error | BaseError | string;
export type ErrorResult<T> = { success: true; data: T } | { success: false; error: BaseError };

// Type guards
export function isApiError(error: unknown): error is ApiError {
  return typeof error === 'object' && error !== null && 'status' in error;
}

export function isAppError(error: unknown): error is AppError {
  return typeof error === 'object' && error !== null && 'type' in error && 'recoverable' in error;
}

export function isComponentError(error: unknown): error is ComponentError {
  return typeof error === 'object' && error !== null && 'componentName' in error;
}

export function isValidationError(error: unknown): error is ValidationError {
  return typeof error === 'object' && error !== null && 'field' in error && 'constraint' in error;
}

export function isNetworkError(error: unknown): error is NetworkError {
  return typeof error === 'object' && error !== null && ('timeout' in error || 'offline' in error);
}