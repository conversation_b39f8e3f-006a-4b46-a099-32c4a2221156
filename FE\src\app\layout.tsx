import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Merriweather, Source_Serif_4, Noto_Serif, <PERSON>o, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Toaster } from "@/components/ui/sonner";
import { MainLayout } from "@/components/layout/MainLayout";

const inter = Inter({ subsets: ["latin"] });
const merriweather = Merriweather({ 
  subsets: ["latin"], 
  weight: ["300", "400", "700"],
  variable: "--font-merriweather"
});
const sourceSerif = Source_Serif_4({ 
  subsets: ["latin"], 
  weight: ["300", "400", "600"],
  variable: "--font-source-serif"
});
const notoSerif = Noto_Serif({ 
  subsets: ["latin"], 
  weight: ["300", "400", "600"],
  variable: "--font-noto-serif"
});
const roboto = Roboto({ 
  subsets: ["latin"], 
  weight: ["300", "400", "500", "700"],
  variable: "--font-roboto"
});
const jetbrainsMono = JetBrains_Mono({ 
  subsets: ["latin"], 
  weight: ["300", "400", "500", "600"],
  variable: "--font-jetbrains-mono"
});

export const metadata: Metadata = {
  title: "WebTruyen - Your Story Hub",
  description: "A modern web application for scraping and reading stories.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full dark">
      <body className={`${inter.className} ${merriweather.variable} ${sourceSerif.variable} ${notoSerif.variable} ${roboto.variable} ${jetbrainsMono.variable} flex flex-col h-full`}>
        <Toaster />
        <MainLayout header={<Navbar />} footer={<Footer />}>
          {children}
        </MainLayout>
      </body>
    </html>
  );
}