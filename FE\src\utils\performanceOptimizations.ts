/**
 * Performance Optimization Utilities
 * Provides memory cleanup, network optimization, and performance monitoring
 */

import { toast } from 'sonner';

// Memory management utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private cleanupTasks: (() => void)[] = [];
  private memoryThreshold = 50 * 1024 * 1024; // 50MB threshold
  private isMonitoring = false;

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * Register a cleanup task to be executed when memory pressure is detected
   */
  registerCleanupTask(task: () => void): () => void {
    this.cleanupTasks.push(task);
    return () => {
      const index = this.cleanupTasks.indexOf(task);
      if (index > -1) {
        this.cleanupTasks.splice(index, 1);
      }
    };
  }

  /**
   * Execute all registered cleanup tasks
   */
  executeCleanup(): void {
    // Executing memory cleanup tasks
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        // Cleanup task failed
      }
    });
  }

  /**
   * Start monitoring memory usage
   */
  startMonitoring(): void {
    if (this.isMonitoring || typeof window === 'undefined') return;
    
    this.isMonitoring = true;
    
    // Monitor memory usage if available
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory;
        if (memory && memory.usedJSHeapSize > this.memoryThreshold) {
          // High memory usage detected, executing cleanup
          this.executeCleanup();
        }
      };
      
      // Check memory every 30 seconds
      setInterval(checkMemory, 30000);
    }
  }

  /**
   * Stop monitoring memory usage
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
  }
}

// Network optimization utilities
export class NetworkOptimizer {
  private static requestQueue: Map<string, Promise<any>> = new Map();
  private static maxConcurrentRequests = 6;
  private static activeRequests = 0;
  private static pendingRequests: Array<() => void> = [];

  /**
   * Deduplicate identical requests
   */
  static async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.requestQueue.has(key)) {
      return this.requestQueue.get(key)!;
    }

    const promise = requestFn().finally(() => {
      this.requestQueue.delete(key);
    });

    this.requestQueue.set(key, promise);
    return promise;
  }

  /**
   * Throttle concurrent requests
   */
  static async throttleRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const executeRequest = async () => {
        if (this.activeRequests >= this.maxConcurrentRequests) {
          this.pendingRequests.push(executeRequest);
          return;
        }

        this.activeRequests++;
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests--;
          const nextRequest = this.pendingRequests.shift();
          if (nextRequest) {
            nextRequest();
          }
        }
      };

      executeRequest();
    });
  }

  /**
   * Check network connection quality
   */
  static getConnectionQuality(): 'slow' | 'fast' | 'unknown' {
    if (typeof navigator === 'undefined' || !('connection' in navigator)) {
      return 'unknown';
    }

    const connection = (navigator as any).connection;
    if (!connection) return 'unknown';

    // Consider 2G and slow-2g as slow
    if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
      return 'slow';
    }

    return 'fast';
  }

  /**
   * Optimize request based on connection quality
   */
  static optimizeForConnection<T>(fastFn: () => Promise<T>, slowFn: () => Promise<T>): Promise<T> {
    const quality = this.getConnectionQuality();
    return quality === 'slow' ? slowFn() : fastFn();
  }
}

// Cache optimization utilities
export class CacheOptimizer {
  private static readonly MAX_CACHE_SIZE = 100;
  private static readonly CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private static cleanupInterval: NodeJS.Timeout | null = null;

  /**
   * Start automatic cache cleanup
   */
  static startCleanup(cacheMap: Map<string, any>, maxSize: number = this.MAX_CACHE_SIZE): void {
    if (this.cleanupInterval) return;

    this.cleanupInterval = setInterval(() => {
      this.cleanupCache(cacheMap, maxSize);
    }, this.CACHE_CLEANUP_INTERVAL);
  }

  /**
   * Stop automatic cache cleanup
   */
  static stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Clean up cache based on LRU strategy
   */
  static cleanupCache(cacheMap: Map<string, any>, maxSize: number): void {
    if (cacheMap.size <= maxSize) return;

    const entries = Array.from(cacheMap.entries());
    const entriesToRemove = entries.slice(0, cacheMap.size - maxSize);
    
    entriesToRemove.forEach(([key]) => {
      cacheMap.delete(key);
    });

    // Cache cleanup completed
  }

  /**
   * Get cache memory usage estimate
   */
  static estimateCacheSize(cacheMap: Map<string, any>): number {
    let totalSize = 0;
    
    cacheMap.forEach((value, key) => {
      // Rough estimation of memory usage
      totalSize += key.length * 2; // UTF-16 characters
      totalSize += JSON.stringify(value).length * 2;
    });
    
    return totalSize;
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();

  /**
   * Record a performance metric
   */
  static recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  /**
   * Get average of a metric
   */
  static getAverageMetric(name: string): number {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return 0;
    
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * Monitor refresh operation performance
   */
  static async monitorRefresh<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric(`${operationName}_success`, duration);
      
      if (duration > 5000) { // Warn if operation takes more than 5 seconds
        // Slow operation warning removed
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(`${operationName}_error`, duration);
      throw error;
    }
  }
}

// Edge case handling utilities
export class EdgeCaseHandler {
  private static rapidClickTracker = new Map<string, number>();
  private static readonly RAPID_CLICK_THRESHOLD = 1000; // 1 second

  /**
   * Prevent rapid clicking on refresh buttons
   */
  static preventRapidClicks(key: string): boolean {
    const now = Date.now();
    const lastClick = this.rapidClickTracker.get(key);
    
    if (lastClick && now - lastClick < this.RAPID_CLICK_THRESHOLD) {
      return false; // Prevent the action
    }
    
    this.rapidClickTracker.set(key, now);
    return true; // Allow the action
  }

  /**
   * Handle offline mode gracefully
   */
  static handleOfflineMode(operation: () => void): void {
    if (!navigator.onLine) {
      toast.error('You are currently offline. This action will be performed when you reconnect.', {
        duration: 5000,
        action: {
          label: 'Retry',
          onClick: () => {
            if (navigator.onLine) {
              operation();
            } else {
              toast.error('Still offline. Please check your connection.');
            }
          }
        }
      });
      return;
    }
    
    operation();
  }

  /**
   * Handle concurrent operations
   */
  static async handleConcurrentOperations<T>(
    operations: Array<() => Promise<T>>,
    maxConcurrency: number = 3
  ): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];
    
    for (const operation of operations) {
      const promise = operation().then(result => {
        results.push(result);
      });
      
      executing.push(promise);
      
      if (executing.length >= maxConcurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }
    
    await Promise.all(executing);
    return results;
  }
}

// Initialize memory monitoring
if (typeof window !== 'undefined') {
  MemoryManager.getInstance().startMonitoring();
}