'use client';

import React, { ReactNode, useCallback, createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import SwipeGestures from '@/components/ui/SwipeGestures';
import { useChaptersCache } from '@/hooks/useChaptersCache';
import { toast } from 'sonner';
import { EdgeCaseHandler } from '@/utils/performanceOptimizations';

// Context for providing refresh functionality to child components
interface RefreshContextType {
  onRefresh: () => void;
  isRefreshing: boolean;
  isAvailable: boolean;
  refreshKey: number; // Add refresh key to force re-renders
}

interface PageContextType {
  currentPage: number;
  setCurrentPage: (page: number) => void;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);
const PageContext = createContext<PageContextType | undefined>(undefined);

export const useRefreshContext = () => {
  const context = useContext(RefreshContext);
  // Graceful degradation: return null if context is not available
  // instead of throwing an error
  return context || null;
};

// Safe hook that provides fallback values
export const useRefreshContextSafe = () => {
  const context = useContext(RefreshContext);
  return {
    onRefresh: context?.onRefresh || (() => {}),
    isRefreshing: context?.isRefreshing || false,
    isAvailable: !!context,
    refreshKey: context?.refreshKey || 0
  };
};

export const usePageContext = () => {
  const context = useContext(PageContext);
  if (!context) {
    throw new Error('usePageContext must be used within a PageContext provider');
  }
  return context;
};

export const usePageContextSafe = () => {
  const context = useContext(PageContext);
  return {
    currentPage: context?.currentPage || 1,
    setCurrentPage: context?.setCurrentPage || (() => {}),
    isAvailable: !!context
  };
};

interface StoryDetailWrapperProps {
  children: ReactNode;
  storyId: string;
  onRefresh?: () => void;
  currentPage?: number; // Accept currentPage from child component
}

const StoryDetailWrapper: React.FC<StoryDetailWrapperProps> = ({
  children,
  storyId,
  onRefresh,
  currentPage: propCurrentPage
}) => {
  const router = useRouter();
  const { chapters, refreshChapters, loading } = useChaptersCache(storyId);
  const [isOnline, setIsOnline] = useState(typeof navigator !== 'undefined' ? navigator.onLine : true);
  const [currentPage, setCurrentPage] = useState(propCurrentPage || 1);
  const [refreshKey, setRefreshKey] = useState(0); // Add refresh key state
  
  // Debug: Log refreshKey changes
  useEffect(() => {
    // RefreshKey monitoring for debugging
  }, [refreshKey, storyId]);
  
  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRefresh = useCallback(async () => {
    if (loading) return; // Prevent multiple simultaneous refreshes
    
    // Prevent rapid clicking
    const rapidClickKey = `refresh-${storyId}`;
    if (!EdgeCaseHandler.preventRapidClicks(rapidClickKey)) {
      toast.info('Please wait before refreshing again.', { duration: 2000 });
      return;
    }
    
    // Check network status before attempting refresh
    if (!navigator.onLine) {
      EdgeCaseHandler.handleOfflineMode(() => handleRefresh());
      return;
    }
    
    try {
      const result = await refreshChapters(undefined, currentPage);
      if (result.success) {
        // Increment refresh key to force re-render of child components
        setRefreshKey(prev => {
          const newKey = prev + 1;
          return newKey;
        });
        
        toast.success('Chapters refreshed successfully!', {
          duration: 2000
        });
        // Call the optional onRefresh callback if provided
        onRefresh?.();
      } else {
        // Enhanced error handling with retry option for certain errors
        const isRetryableError = result.error?.includes('network') || 
                                result.error?.includes('timeout') ||
                                result.error?.includes('server error');
        
        toast.error(result.error || 'Failed to refresh chapters', {
          duration: isRetryableError ? 8000 : 5000,
          action: isRetryableError ? {
            label: 'Retry',
            onClick: () => handleRefresh()
          } : undefined
        });
      }
    } catch (error) {
      // Fallback error handling
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh chapters';
      const isNetworkError = errorMessage.includes('fetch') || 
                            errorMessage.includes('network') ||
                            errorMessage.includes('timeout');
      
      toast.error(isNetworkError ? 
        'Network error occurred. Please check your connection and try again.' : 
        'An unexpected error occurred while refreshing chapters.', {
        duration: 6000,
        action: {
          label: 'Retry',
          onClick: () => handleRefresh()
        }
      });
    }
  }, [refreshChapters, loading, onRefresh, currentPage]);

  const handleSwipeLeft = useCallback(() => {
    // Swipe left = next chapter (go to first available chapter)
    if (chapters.length > 0) {
      const firstChapter = chapters.find(ch => ch.is_scraped);
      if (firstChapter) {
        router.push(`/stories/${storyId}/${firstChapter.chapter_number}`);
        toast.success(`Opening Chapter ${firstChapter.chapter_number}`);
      } else {
        toast.info('No chapters available to read');
      }
    }
  }, [chapters, router, storyId]);

  const handleSwipeRight = useCallback(() => {
    // Swipe right = go back to home/stories list
    router.push('/');
    toast.info('Returning to stories list');
  }, [router]);

  const handleSwipeUp = useCallback(() => {
    // Swipe up = scroll to top of page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleSwipeDown = useCallback(() => {
    // Swipe down = scroll to chapter list
    const chapterListElement = document.querySelector('[data-chapter-list]');
    if (chapterListElement) {
      chapterListElement.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Fallback: scroll to bottom
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }
  }, []);

  return (
    <RefreshContext.Provider value={{ 
      onRefresh: handleRefresh, 
      isRefreshing: loading,
      isAvailable: isOnline && !!refreshChapters,
      refreshKey
    }}>
      <PageContext.Provider value={{
        currentPage,
        setCurrentPage
      }}>
        <SwipeGestures
          onSwipeLeft={handleSwipeLeft}
          onSwipeRight={handleSwipeRight}
          onSwipeUp={handleSwipeUp}
          onSwipeDown={handleSwipeDown}
          threshold={80}
          className="min-h-screen"
        >
          {children}
        </SwipeGestures>
      </PageContext.Provider>
    </RefreshContext.Provider>
  );
};

export default StoryDetailWrapper;