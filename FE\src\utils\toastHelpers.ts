/**
 * Toast Helper Utilities
 * Provides utility functions for creating enhanced toast notifications
 */

import { toast } from 'sonner';
import { 
  EnhancedToastProps, 
  ToastHelperOptions, 
  BatchActionToastConfig,
  ToastNotificationState
} from '@/types/toast';
import { showEnhancedToast } from '@/components/ui/EnhancedToast';

// Default configuration for batch action toasts
export const DEFAULT_BATCH_TOAST_CONFIG: BatchActionToastConfig = {
  scraping: {
    loading: {
      message: 'Scraping chapters...',
      duration: 0 // No auto-dismiss for loading
    },
    success: {
      message: 'Chapters scraped successfully!',
      duration: 5000, // 5 seconds - longer duration for better UX
      showRefreshButton: true
    },
    error: {
      message: 'Failed to scrape chapters',
      duration: 0 // No auto-dismiss for errors
    },
    cancelled: {
      message: 'Scraping cancelled',
      duration: 3000
    }
  },
  enhancement: {
    loading: {
      message: 'Enhancing chapters...',
      duration: 0
    },
    success: {
      message: 'Chapters enhanced successfully!',
      duration: 5000, // 5 seconds - longer duration for better UX
      showRefreshButton: true
    },
    error: {
      message: 'Failed to enhance chapters',
      duration: 0
    },
    cancelled: {
      message: 'Enhancement cancelled',
      duration: 3000
    }
  }
};

// Global state for toast notifications
let toastState: ToastNotificationState = {
  activeToasts: new Map(),
  refreshOperations: new Set(),
  lastRefreshTime: 0,
  debounceDelay: 1000 // 1 second debounce
};

/**
 * Creates an enhanced toast with optional refresh button
 */
export function createEnhancedToast(
  message: string,
  type: EnhancedToastProps['type'],
  options: ToastHelperOptions = {}
): string {
  const {
    duration = 0,
    showRefreshButton = false,
    onRefresh,
    refreshLoading = false,
    data = {}
  } = options;

  const toastId = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const toastProps: EnhancedToastProps = {
    id: toastId,
    message,
    type,
    duration,
    showRefreshButton,
    dismissible: true,
    refreshButton: showRefreshButton && onRefresh ? {
      onRefresh: () => handleRefreshClick(toastId, onRefresh),
      loading: refreshLoading,
      disabled: refreshLoading || toastState.refreshOperations.has(toastId),
      ariaLabel: 'Refresh chapter list'
    } : undefined
  };

  // Store toast in state
  toastState.activeToasts.set(toastId, toastProps);

  // Use enhanced toast component with refresh button support
  showEnhancedToast(toastProps);

  return toastId;
}

/**
 * Handles refresh button clicks with debouncing
 */
function handleRefreshClick(toastId: string, onRefresh: () => void): void {
  const now = Date.now();
  
  // Check debounce
  if (now - toastState.lastRefreshTime < toastState.debounceDelay) {
    return;
  }

  // Check if refresh is already in progress
  if (toastState.refreshOperations.has(toastId)) {
    return;
  }

  // Starting refresh operation
  toastState.lastRefreshTime = now;
  toastState.refreshOperations.add(toastId);

  try {
    // Executing onRefresh callback
    onRefresh();
    // Refresh callback executed successfully
  } catch (error) {
    // Error during refresh
  } finally {
    // Remove from refresh operations after a delay
    setTimeout(() => {
      toastState.refreshOperations.delete(toastId);
    }, 1000);
  }
}

/**
 * Creates a batch scraping success toast with refresh button
 */
export function createBatchScrapeSuccessToast(
  onRefresh: () => void,
  customMessage?: string
): string {
  const config = DEFAULT_BATCH_TOAST_CONFIG.scraping.success;
  return createEnhancedToast(
    customMessage || config.message,
    'success',
    {
      duration: config.duration,
      showRefreshButton: config.showRefreshButton,
      onRefresh
    }
  );
}

/**
 * Creates a batch enhancement success toast with refresh button
 */
export function createBatchEnhanceSuccessToast(
  onRefresh: () => void,
  customMessage?: string
): string {
  const config = DEFAULT_BATCH_TOAST_CONFIG.enhancement.success;
  return createEnhancedToast(
    customMessage || config.message,
    'success',
    {
      duration: config.duration,
      showRefreshButton: config.showRefreshButton,
      onRefresh
    }
  );
}

/**
 * Creates a batch operation error toast
 */
export function createBatchErrorToast(
  message: string,
  operation: 'scraping' | 'enhancement'
): string {
  const config = DEFAULT_BATCH_TOAST_CONFIG[operation].error;
  return createEnhancedToast(
    message,
    'error',
    {
      duration: config.duration
    }
  );
}

/**
 * Creates a batch operation loading toast
 */
export function createBatchLoadingToast(
  operation: 'scraping' | 'enhancement',
  customMessage?: string
): string {
  const config = DEFAULT_BATCH_TOAST_CONFIG[operation].loading;
  return createEnhancedToast(
    customMessage || config.message,
    'info',
    {
      duration: config.duration
    }
  );
}

/**
 * Creates a batch operation cancelled toast
 */
export function createBatchCancelledToast(
  operation: 'scraping' | 'enhancement',
  customMessage?: string
): string {
  const config = DEFAULT_BATCH_TOAST_CONFIG[operation].cancelled;
  return createEnhancedToast(
    customMessage || config.message,
    'warning',
    {
      duration: config.duration
    }
  );
}

/**
 * Dismisses a specific toast
 */
export function dismissToast(toastId: string): void {
  toast.dismiss(toastId);
  toastState.activeToasts.delete(toastId);
  toastState.refreshOperations.delete(toastId);
}

/**
 * Dismisses all active toasts
 */
export function dismissAllToasts(): void {
  toast.dismiss();
  toastState.activeToasts.clear();
  toastState.refreshOperations.clear();
}

/**
 * Gets the current toast state (for testing/debugging)
 */
export function getToastState(): ToastNotificationState {
  return { ...toastState };
}

/**
 * Resets the toast state (for testing)
 */
export function resetToastState(): void {
  toastState = {
    activeToasts: new Map(),
    refreshOperations: new Set(),
    lastRefreshTime: 0,
    debounceDelay: 1000
  };
}