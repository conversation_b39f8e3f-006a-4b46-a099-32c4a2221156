/**
 * Job Management Types
 * TypeScript interfaces and types for job management functionality
 */

// Job status enumeration
export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Job priority levels
export enum JobPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// Job type enumeration
export enum JobType {
  SCRAPING = 'scraping',
  DATA_PROCESSING = 'data_processing',
  EXPORT = 'export',
  IMPORT = 'import'
}

// Job action enumeration
export enum JobAction {
  VIEW = 'view',
  EDIT = 'edit',
  CANCEL = 'cancel',
  RETRY = 'retry',
  DELETE = 'delete',
  PAUSE = 'pause',
  RESUME = 'resume'
}

// Base job interface
export interface Job {
  id: string;
  title: string;
  description?: string;
  type: JobType;
  status: JobStatus;
  priority: JobPriority;
  progress: number; // 0-100
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  startedAt?: string; // ISO date string
  completedAt?: string; // ISO date string
  errorMessage?: string;
  metadata?: Record<string, any>;
}

// Scraping job specific interface
export interface ScrapingJob extends Job {
  url: string;
  pageLimit?: number;
  scrapingMode: 'traditional' | 'realtime';
  totalPages?: number;
  processedPages?: number;
  extractedChapters?: number;
}

// Job filter options
export interface JobFilter {
  status?: JobStatus[];
  priority?: JobPriority[];
  dateFrom?: string; // ISO date string
  dateTo?: string; // ISO date string
  search?: string; // Search in title/description
}

// Job list filters interface
export interface JobListFilters {
  status?: JobStatus[];
  priority?: JobPriority[];
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'status';
  sortOrder?: 'asc' | 'desc';
}

// Pagination interface
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// API response types
export interface JobListResponse {
  jobs: Job[];
  pagination: Pagination;
}

export interface JobHistoryResponse {
  jobs: Job[];
  pagination: Pagination;
}

export interface JobCreateRequest {
  title: string;
  description?: string;
  type: JobType;
  priority?: JobPriority;
  url?: string;
  pageLimit?: number;
  scrapingMode?: 'traditional' | 'realtime';
  config?: Record<string, any>;
}

export interface JobUpdateRequest {
  title?: string;
  description?: string;
  priority?: JobPriority;
}

// Hook state interfaces
export interface UseJobListState {
  jobs: Job[];
  loading: boolean;
  error: string | null;
  pagination: Pagination;
  filters: JobFilter;
  refreshing: boolean;
}

export interface UseJobListActions {
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  updateFilters: (filters: Partial<JobFilter>) => void;
  cancelJob: (jobId: string) => Promise<void>;
  retryJob: (jobId: string) => Promise<void>;
  deleteJob: (jobId: string) => Promise<void>;
}

// Component prop types
export interface JobListTableProps {
  jobs: Job[];
  loading?: boolean;
  onJobAction?: (jobId: string, action: JobAction) => void;
  onRefresh?: () => void;
  pagination?: Pagination;
  onPageChange?: (page: number) => void;
}

export interface AddJobModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (job: JobCreateRequest) => Promise<void>;
  loading?: boolean;
}

export interface JobStatusBadgeProps {
  status: JobStatus;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

// Error types
export interface JobError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// WebSocket message types for real-time updates
export interface JobProgressUpdate {
  jobId: string;
  progress: number;
  status: JobStatus;
  processedPages?: number;
  totalPages?: number;
  message?: string;
}

export interface JobStatusUpdate {
  jobId: string;
  status: JobStatus;
  completedAt?: string;
  errorMessage?: string;
}

// All types are exported individually above