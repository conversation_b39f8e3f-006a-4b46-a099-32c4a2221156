import { fetchChaptersByStoryId } from '@/services/storyService';
import StoryChapterList from './StoryChapterList';

interface StoryChapterListWrapperProps {
  storyId: string;
}

export default async function StoryChapterListWrapper({ storyId }: StoryChapterListWrapperProps) {
  const response = await fetchChaptersByStoryId(storyId);
  const chapters = response.data;

  return <StoryChapterList storyId={storyId} initialChapters={chapters} />;
}