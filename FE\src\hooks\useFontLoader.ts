'use client';

import { useState, useEffect } from 'react';

interface FontLoadStatus {
  [fontFamily: string]: boolean;
}

export const useFontLoader = () => {
  const [fontLoadStatus, setFontLoadStatus] = useState<FontLoadStatus>({});
  const [allFontsLoaded, setAllFontsLoaded] = useState(false);

  useEffect(() => {
    // Check if document.fonts API is available
    if (!document.fonts) {
      console.warn('Font Loading API not supported');
      setAllFontsLoaded(true);
      return;
    }

    const checkFontLoad = async () => {
      const fontsToCheck = [
        'Inter',
        'Merriweather', 
        'Source Serif 4',
        'Noto Serif',
        'Roboto',
        'JetBrains Mono'
      ];

      const status: FontLoadStatus = {};
      
      for (const fontFamily of fontsToCheck) {
        try {
          // Check if font is loaded
          const isLoaded = document.fonts.check(`16px "${fontFamily}"`);
          status[fontFamily] = isLoaded;
          
          if (!isLoaded) {
            // Try to load the font
            await document.fonts.load(`16px "${fontFamily}"`);
            status[fontFamily] = document.fonts.check(`16px "${fontFamily}"`);
          }
        } catch (error) {
          console.warn(`Failed to check/load font ${fontFamily}:`, error);
          status[fontFamily] = false;
        }
      }

      setFontLoadStatus(status);
      
      // Check if all fonts are loaded
      const allLoaded = Object.values(status).every(loaded => loaded);
      setAllFontsLoaded(allLoaded);

      // DEBUG LOGS
      console.log('🔤 Font Load Status:', {
        status,
        allLoaded,
        timestamp: new Date().toISOString()
      });
    };

    // Initial check
    checkFontLoad();

    // Listen for font load events
    const handleFontLoad = () => {
      checkFontLoad();
    };

    document.fonts.addEventListener('loadingdone', handleFontLoad);
    document.fonts.addEventListener('loadingerror', handleFontLoad);

    return () => {
      document.fonts.removeEventListener('loadingdone', handleFontLoad);
      document.fonts.removeEventListener('loadingerror', handleFontLoad);
    };
  }, []);

  return {
    fontLoadStatus,
    allFontsLoaded,
    isFontLoaded: (fontFamily: string) => fontLoadStatus[fontFamily] || false
  };
};
