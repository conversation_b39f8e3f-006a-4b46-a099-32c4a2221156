// ============================================================================
// Unified Scraping Types
// ============================================================================

export type WorkflowType = 'simple' | 'hierarchical';

export type ScrapingPhase = 
  | 'initialization'
  | 'discovery'
  | 'content-extraction'
  | 'enhancement'
  | 'finalization';

export interface WorkflowInfo {
  id: WorkflowType;
  name: string;
  description: string;
  icon: string;
  features: string[];
  limitations: string[];
  estimatedTime: string;
  resourceUsage: 'low' | 'medium' | 'high';
  complexity: 'beginner' | 'intermediate' | 'advanced';
  bestFor: string[];
}

export interface ScrapingConfiguration {
  workflow: WorkflowType;
  url: string;
  // Simple workflow config
  maxPages?: number;
  // Hierarchical workflow config
  scrapeContent?: boolean;
  maxConcurrent?: number;
  rateLimit?: number;
  // Common config
  enableNotifications?: boolean;
  autoRetry?: boolean;
  maxRetries?: number;
  // Additional config options
  enableImages?: boolean;
  retryAttempts?: number;
  delayBetweenRequests?: number;
  concurrency?: number;
  timeout?: number;
  enableChapterEnhancement?: boolean;
  skipExistingChapters?: boolean;
}

export interface UnifiedProgress {
  workflow: WorkflowType;
  phase: string;
  currentPhase?: ScrapingPhase;
  currentStep: number;
  totalSteps: number;
  percentage: number;
  message: string;
  estimatedTimeRemaining?: number;
  startTime: Date;
  data?: any;
  errors?: Array<{ message: string; details?: string; timestamp: number }>;
  status?: 'running' | 'completed' | 'failed' | 'cancelled';
  overall?: {
    percentage: number;
    processed: number;
    total: number;
    successful: number;
  };
  phases?: Record<ScrapingPhase, {
    percentage: number;
    processed: number;
    total: number;
    successful: number;
    failed: number;
    recentActivity?: string[];
  }>;
  performance?: {
    averageSpeed: number;
    peakSpeed: number;
    errorRate: number;
  };
}

export interface WorkflowRecommendation {
  recommendedWorkflow: WorkflowType;
  confidence: number;
  reasons: string[];
  warnings?: string[];
  estimatedBenefits: string[];
}

export interface ScrapingResult {
  workflow: WorkflowType;
  success: boolean;
  storyId?: string;
  title?: string;
  totalPages?: number;
  totalChapters?: number;
  scrapedChapters?: number;
  duration: number;
  errors?: string[];
  warnings?: string[];
}

export interface WorkflowComparison {
  feature: string;
  simple: string | boolean;
  hierarchical: string | boolean;
  winner?: WorkflowType;
}

export interface ScrapingStats {
  totalScrapes: number;
  successRate: number;
  averageDuration: number;
  preferredWorkflow: WorkflowType;
  recentActivity: {
    date: string;
    workflow: WorkflowType;
    success: boolean;
    duration: number;
  }[];
}

export interface ConfigurationPreset {
  id: string;
  name: string;
  description: string;
  workflow: WorkflowType;
  config: Partial<ScrapingConfiguration>;
  tags: string[];
  isDefault?: boolean;
}

export interface NotificationSettings {
  enabled: boolean;
  onStart: boolean;
  onProgress: boolean;
  onComplete: boolean;
  onError: boolean;
  sound: boolean;
  desktop: boolean;
}

export interface UserPreferences {
  defaultWorkflow: WorkflowType;
  notifications: NotificationSettings;
  autoSaveConfig: boolean;
  showAdvancedOptions: boolean;
  theme: 'dark' | 'light' | 'auto';
  language: string;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface UnifiedScrapingResponse {
  success: boolean;
  workflow: WorkflowType;
  data: ScrapingResult;
  message: string;
  timestamp: string;
}

export interface ProgressUpdateResponse {
  jobId: string;
  progress: UnifiedProgress;
  timestamp: string;
}

export interface WorkflowAnalysisResponse {
  url: string;
  analysis: {
    estimatedPages: number;
    estimatedChapters: number;
    complexity: 'low' | 'medium' | 'high';
    siteType: string;
  };
  recommendation: WorkflowRecommendation;
  timestamp: string;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface WorkflowSelectorProps {
  selectedWorkflow: WorkflowType;
  onWorkflowChange: (workflow: WorkflowType) => void;
  disabled?: boolean;
  showComparison?: boolean;
}

export interface ConfigPanelProps {
  workflow: WorkflowType;
  config: ScrapingConfiguration;
  onConfigChange: (config: Partial<ScrapingConfiguration>) => void;
  disabled?: boolean;
  showAdvanced?: boolean;
}

export interface ProgressTrackerProps {
  workflow: WorkflowType;
  progress: UnifiedProgress | null;
  isActive?: boolean;
  showDetails?: boolean;
  onCancel?: () => void;
}

export interface ResultsViewerProps {
  result: ScrapingResult | null;
  onViewStory?: (storyId: string) => void;
  onRetry?: () => void;
  onNewScrape?: () => void;
}

// ============================================================================
// Hook Return Types
// ============================================================================

export interface UseScrapingWorkflowReturn {
  workflow: WorkflowType;
  setWorkflow: (workflow: WorkflowType) => void;
  config: ScrapingConfiguration;
  updateConfig: (updates: Partial<ScrapingConfiguration>) => void;
  isValid: boolean;
  errors: string[];
  recommendation: WorkflowRecommendation | null;
  getRecommendation: (url: string) => Promise<void>;
  isAnalyzing: boolean;
}

export interface UseProgressTrackingReturn {
  progress: UnifiedProgress | null;
  isActive: boolean;
  startTracking: (jobId?: string) => void;
  stopTracking: () => void;
  clearProgress: () => void;
  updateProgress: (progress: UnifiedProgress) => void;
  formatDuration: (milliseconds: number) => string;
  formatSpeed: (speed: number) => string;
  getProgressColor: (percentage: number) => string;
}

export interface UseScrapingConfigReturn {
  config: ScrapingConfiguration;
  updateConfig: (updates: Partial<ScrapingConfiguration>) => void;
  resetConfig: () => void;
  saveConfig: (name: string) => void;
  loadConfig: (preset: ConfigurationPreset) => void;
  presets: ConfigurationPreset[];
  isValid: boolean;
  errors: string[];
}