'use client';

import { useState, useEffect } from 'react';

type Theme = 'dark' | 'light' | 'sepia';
type FontFamily = 'inter' | 'georgia' | 'times' | 'merriweather' | 'source-serif' | 'noto-serif' | 'roboto' | 'jetbrains-mono';

interface ReaderSettings {
  fontSize: number;
  lineHeight: number;
  theme: Theme;
  fontFamily: FontFamily;
}

const DEFAULT_SETTINGS: ReaderSettings = {
  fontSize: 18,
  lineHeight: 1.8,
  theme: 'dark',
  fontFamily: 'inter'
};

// Font family configurations optimized for reading
export const FONT_FAMILIES = {
  inter: {
    name: 'Inter',
    displayName: 'Inter (Modern)',
    cssFamily: '"Inter", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif',
    description: 'Clean and modern sans-serif, excellent for digital reading'
  },
  georgia: {
    name: 'Georgia',
    displayName: 'Georgia (Classic)',
    cssFamily: '"Georgia", "Times New Roman", Times, serif',
    description: 'Traditional serif font, great for long-form reading'
  },
  times: {
    name: 'Times New Roman',
    displayName: 'Times New Roman',
    cssFamily: '"Times New Roman", Times, serif',
    description: 'Classic newspaper font, familiar and readable'
  },
  merriweather: {
    name: 'Merriweather',
    displayName: 'Merriweather (Elegant)',
    cssFamily: 'var(--font-merriweather), Georgia, serif',
    description: 'Designed for screens, excellent readability'
  },
  'source-serif': {
    name: 'Source Serif Pro',
    displayName: 'Source Serif Pro',
    cssFamily: 'var(--font-source-serif), Georgia, serif',
    description: 'Adobe\'s serif font optimized for reading'
  },
  'noto-serif': {
    name: 'Noto Serif',
    displayName: 'Noto Serif (Universal)',
    cssFamily: 'var(--font-noto-serif), Georgia, serif',
    description: 'Google\'s universal serif font with excellent language support'
  },
  roboto: {
    name: 'Roboto',
    displayName: 'Roboto (Google)',
    cssFamily: 'var(--font-roboto), "Roboto", "Helvetica Neue", Arial, sans-serif',
    description: 'Google\'s signature font, clean and highly readable'
  },
  'jetbrains-mono': {
    name: 'JetBrains Mono',
    displayName: 'JetBrains Mono (Code)',
    cssFamily: 'var(--font-jetbrains-mono), "JetBrains Mono", "Fira Code", monospace',
    description: 'Monospace font designed for developers, excellent for code-like text'
  }
} as const;

const STORAGE_KEY = 'reader-settings';

export const useReaderSettings = () => {
  const [settings, setSettings] = useState<ReaderSettings>(DEFAULT_SETTINGS);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored) as ReaderSettings;
        // Validate settings
        const validatedSettings = {
          fontSize: Math.min(Math.max(parsedSettings.fontSize || DEFAULT_SETTINGS.fontSize, 12), 28),
          lineHeight: Math.min(Math.max(parsedSettings.lineHeight || DEFAULT_SETTINGS.lineHeight, 1.2), 2.5),
          theme: ['dark', 'light', 'sepia'].includes(parsedSettings.theme) ? parsedSettings.theme : DEFAULT_SETTINGS.theme,
          fontFamily: Object.keys(FONT_FAMILIES).includes(parsedSettings.fontFamily) ? parsedSettings.fontFamily : DEFAULT_SETTINGS.fontFamily
        };
        setSettings(validatedSettings);
      }
    } catch (error) {
      console.warn('Failed to load reader settings:', error);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    if (isLoaded) {
      try {
        // DEBUG LOGS - Validate settings save
        console.log('💾 useReaderSettings Save:', {
          settings,
          fontFamily: settings.fontFamily,
          cssFamily: FONT_FAMILIES[settings.fontFamily]?.cssFamily,
          timestamp: new Date().toISOString()
        });
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.warn('Failed to save reader settings:', error);
      }
    }
  }, [settings, isLoaded]);

  // Apply theme to document body
  useEffect(() => {
    if (isLoaded) {
      document.body.className = document.body.className.replace(/theme-\w+/g, '');
      document.body.classList.add(`theme-${settings.theme}`);
    }
  }, [settings.theme, isLoaded]);

  const setFontSize = (fontSize: number) => {
    const validatedSize = Math.min(Math.max(fontSize, 12), 28);
    setSettings(prev => ({ ...prev, fontSize: validatedSize }));
  };

  const setLineHeight = (lineHeight: number) => {
    const validatedHeight = Math.min(Math.max(lineHeight, 1.2), 2.5);
    setSettings(prev => ({ ...prev, lineHeight: validatedHeight }));
  };

  const setTheme = (theme: Theme) => {
    setSettings(prev => ({ ...prev, theme }));
  };

  const setFontFamily = (fontFamily: FontFamily) => {
    setSettings(prev => ({ ...prev, fontFamily }));
  };

  const resetSettings = () => {
    setSettings(DEFAULT_SETTINGS);
  };

  const increaseFontSize = () => {
    setFontSize(settings.fontSize + 2);
  };

  const decreaseFontSize = () => {
    setFontSize(settings.fontSize - 2);
  };

  return {
    ...settings,
    isLoaded,
    setFontSize,
    setLineHeight,
    setTheme,
    setFontFamily,
    resetSettings,
    increaseFontSize,
    decreaseFontSize
  };
};