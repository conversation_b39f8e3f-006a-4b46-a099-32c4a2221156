/**
 * Next.js Global Error Handler
 * Catches errors that occur in the root layout and provides app-level error handling
 */

'use client';

import { useEffect } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { handleError, createAppError } from '@/utils/errorHandling';
import { AppErrorType } from '@/types/errors';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    // Log the global error when the component mounts
    const appError = createAppError(
      error.message,
      AppErrorType.CLIENT,
      false, // Global errors are typically not recoverable
      'A critical error occurred in the application',
      {
        digest: error.digest,
        stack: error.stack,
        name: error.name,
        global: true
      }
    );

    handleError(appError, {
      showToast: false, // Don't show toast on global error pages
      logError: true,
      context: {
        page: 'global-error',
        digest: error.digest,
        critical: true
      }
    });
  }, [error]);

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleCopyError = () => {
    const errorDetails = {
      type: 'GLOBAL_ERROR',
      message: error.message,
      name: error.name,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      stack: error.stack
    };
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
  };

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
          <div className="w-full max-w-lg bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h1 className="text-xl font-semibold text-red-900 dark:text-red-100 mb-2">
                Critical Application Error
              </h1>
              <p className="text-red-700 dark:text-red-300 mb-6">
                A critical error occurred that prevented the application from loading properly.
              </p>
            </div>
            
            <div className="px-6 pb-6 space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="flex items-start">
                  <Bug className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Error Details
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300 font-mono break-words">
                      {error.message}
                      {error.digest && (
                        <div className="mt-1 text-xs opacity-70">
                          Error ID: {error.digest}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-2 sm:flex-row">
                <button
                  onClick={reset}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </button>
                <button
                  onClick={handleReload}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reload Page
                </button>
              </div>
              
              <div className="flex flex-col gap-2 sm:flex-row">
                <button
                  onClick={handleGoHome}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </button>
                <button
                  onClick={handleCopyError}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  <Bug className="mr-2 h-4 w-4" />
                  Copy Error
                </button>
              </div>
              
              {process.env.NODE_ENV === 'development' && error.stack && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                    Stack Trace (Development)
                  </summary>
                  <pre className="mt-2 whitespace-pre-wrap text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded border overflow-auto max-h-40 text-gray-800 dark:text-gray-200">
                    {error.stack}
                  </pre>
                </details>
              )}
              
              <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                This is a critical error that requires immediate attention.
                <br />
                Please contact support if this problem persists.
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}