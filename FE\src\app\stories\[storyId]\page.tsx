import { fetchStoryById } from '@/services/storyService';
import StoryInfo from '@/components/story/StoryInfo';
import { Suspense } from 'react';
import StoryChapterListWrapper from '@/components/story/StoryChapterList/StoryChapterListWrapper';
import Loading from '@/components/story/StoryChapterList/Loading';
import { notFound } from 'next/navigation';
import StoryDetailWrapper from '@/components/story/StoryDetailWrapper';

interface StoryPageProps {
  params: {
    storyId: string;
  };
}

export default async function StoryPage({ params }: StoryPageProps) {
  try {
    // Only fetch story info on server-side, chapters will be loaded client-side
    const story = await fetchStoryById(params.storyId);

    return (
      <StoryDetailWrapper storyId={params.storyId}>
        <div className="container mx-auto px-0 sm:px-4 py-4 sm:py-8">
          <StoryInfo story={story} />
          <Suspense fallback={<Loading />}>
            <StoryChapterListWrapper storyId={params.storyId} />
          </Suspense>
        </div>
      </StoryDetailWrapper>
    );
  } catch (error) {
    console.error('Error fetching story data:', error);
    notFound();
  }
}

export async function generateMetadata({ params }: StoryPageProps) {
  try {
    const story = await fetchStoryById(params.storyId);
    return {
      title: `${story.title} - WebTruyen`,
      description: story.description || `Read ${story.title} by author ${story.author}`,
    };
  } catch {
    return {
      title: 'Story Not Found - WebTruyen',
    };
  }
}