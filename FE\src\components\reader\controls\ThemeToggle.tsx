"use client"

import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Moon, Sun } from 'lucide-react';

interface ThemeToggleProps {
  isDarkMode: boolean;
  onToggle: (isDark: boolean) => void;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ isDarkMode, onToggle }) => {
  return (
    <div className="flex items-center space-x-2">
      <Sun className="h-4 w-4 text-muted-foreground" />
      <Switch
        id="theme-toggle"
        checked={isDarkMode}
        onCheckedChange={onToggle}
      />
      <Moon className="h-4 w-4 text-muted-foreground" />
      <Label htmlFor="theme-toggle" className="text-sm text-muted-foreground">
        {isDarkMode ? 'Dark' : 'Light'} Mode
      </Label>
    </div>
  );
};

export default ThemeToggle;