// Types for Enhanced Batch Operations Progress Tracking

export type BatchOperationType = 'scraping' | 'enhancement';

export type BatchOperationStatus = 'idle' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface BatchError {
  itemId: string;
  itemName: string;
  error: string;
  timestamp: Date;
  retryable: boolean;
}

export interface BatchProgressItem {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface BatchProgress {
  id: string;
  type: BatchOperationType;
  status: BatchOperationStatus;
  totalItems: number;
  completedItems: number;
  failedItems: number;
  currentItem?: BatchProgressItem;
  items: BatchProgressItem[];
  errors: BatchError[];
  startTime: Date;
  endTime?: Date;
  estimatedTimeRemaining?: number;
  progressPercentage: number;
}

export interface BatchOperationConfig {
  type: BatchOperationType;
  items: Array<{ id: string; name: string }>;
  storyId: string;
  options?: Record<string, any>;
}

export interface BatchOperationResult {
  success: boolean;
  totalItems: number;
  completedItems: number;
  failedItems: number;
  errors: BatchError[];
  duration: number;
}

// Progress tracking events
export type BatchProgressEvent = 
  | { type: 'BATCH_STARTED'; payload: BatchProgress }
  | { type: 'BATCH_PROGRESS'; payload: Partial<BatchProgress> }
  | { type: 'ITEM_STARTED'; payload: { batchId: string; item: BatchProgressItem } }
  | { type: 'ITEM_COMPLETED'; payload: { batchId: string; itemId: string } }
  | { type: 'ITEM_FAILED'; payload: { batchId: string; itemId: string; error: string } }
  | { type: 'BATCH_COMPLETED'; payload: { batchId: string; result: BatchOperationResult } }
  | { type: 'BATCH_CANCELLED'; payload: { batchId: string } };

// Storage interface for persistent progress
export interface PersistedBatchProgress {
  [batchId: string]: {
    progress: BatchProgress;
    lastUpdated: Date;
    expiresAt: Date;
  };
}

// Hook return types
export interface UseBatchProgressReturn {
  currentBatch: BatchProgress | null;
  allBatches: BatchProgress[];
  isAnyBatchRunning: boolean;
  startBatch: (config: BatchOperationConfig) => Promise<string>;
  cancelBatch: (batchId: string) => Promise<void>;
  retryFailedItems: (batchId: string) => Promise<void>;
  clearCompletedBatches: () => void;
  getBatchById: (batchId: string) => BatchProgress | null;
}

export interface UsePersistentProgressReturn {
  saveProgress: (progress: BatchProgress) => void;
  loadProgress: (batchId: string) => BatchProgress | null;
  loadAllProgress: () => BatchProgress[];
  clearProgress: (batchId: string) => void;
  clearExpiredProgress: () => void;
}

// Component props
export interface ProgressTrackerProps {
  batchId: string;
  onCancel?: () => void;
  onMinimize?: () => void;
  className?: string;
}

export interface BatchResultsModalProps {
  batch: BatchProgress;
  isOpen: boolean;
  onClose: () => void;
  onRetryFailed?: () => void;
  onViewResults?: () => void;
}

export interface BackgroundProgressIndicatorProps {
  batches: BatchProgress[];
  onClick?: (batchId: string) => void;
  className?: string;
}

// Service interfaces
export interface BatchProgressService {
  startBatch: (config: BatchOperationConfig) => Promise<string>;
  getBatchProgress: (batchId: string) => Promise<BatchProgress>;
  cancelBatch: (batchId: string) => Promise<void>;
  retryFailedItems: (batchId: string, itemIds: string[]) => Promise<void>;
  subscribeToBatchUpdates: (batchId: string, callback: (progress: BatchProgress) => void) => () => void;
}