'use client';

import { useState, useEffect, useCallback } from 'react';
import { Chapter } from '@/types/story';
import { fetchChaptersByStoryId } from '@/services/storyService';
import { createComponentError, handleError, safeAsync, getUserFriendlyMessage, normalizeError, retryWithBackoff } from '@/utils/errorHandling';
import { ErrorLike, isNetworkError, isApiError } from '@/types/errors';
import { MemoryManager, NetworkOptimizer, CacheOptimizer, PerformanceMonitor } from '@/utils/performanceOptimizations';

interface ChaptersCacheEntry {
  chapters: Chapter[];
  totalChapters: number;
  lastFetched: number;
  isComplete: boolean;
}

interface ChapterFilters {
  enhanced_only?: boolean;
  scraped_only?: boolean;
}

const chaptersCache = new Map<string, ChaptersCacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Initialize performance optimizations
CacheOptimizer.startCleanup(chaptersCache, 50);

// Register memory cleanup for chapters cache
MemoryManager.getInstance().registerCleanupTask(() => {
  const cacheSize = CacheOptimizer.estimateCacheSize(chaptersCache);
  if (cacheSize > 10 * 1024 * 1024) { // 10MB threshold
    CacheOptimizer.cleanupCache(chaptersCache, 25); // Reduce to 25 entries
  }
});

export const useChaptersCache = (storyId: string, initialChapters?: Chapter[]) => {
  const [chapters, setChapters] = useState<Chapter[]>(initialChapters || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [totalChapters, setTotalChapters] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [pageSize, setPageSize] = useState(20);

  const isCacheValid = useCallback((entry: ChaptersCacheEntry) => {
    return Date.now() - entry.lastFetched < CACHE_DURATION;
  }, []);

  const revalidate = useCallback(async (
    page: number = 1,
    pageSizeParam: number = pageSize,
    filters?: ChapterFilters
  ) => {
    const filterKey = filters ? `${filters.enhanced_only || false}-${filters.scraped_only || false}` : 'none';
    const cacheKey = `${storyId}-${page}-${pageSizeParam}-${filterKey}`;

    const [error, response] = await safeAsync(async () => {
      return await fetchChaptersByStoryId(storyId, page, pageSizeParam, filters, {
        disableCache: true // Always disable cache for revalidation
      });
    });

    if (error) {
      return;
    }

    if (!response) {
      return;
    }

    const newEntry: ChaptersCacheEntry = {
      chapters: response.data,
      totalChapters: response.pagination.total_items,
      lastFetched: Date.now(),
      isComplete: !response.pagination.has_next
    };

    chaptersCache.set(cacheKey, newEntry);

    // Always update state since we don't track currentPage internally anymore
    // Force new array reference to trigger useEffect dependencies
    setChapters([...response.data]);
    setTotalChapters(response.pagination.total_items);
    setTotalPages(Math.ceil(response.pagination.total_items / pageSizeParam));
  }, [storyId, pageSize]);

  const loadChapters = useCallback(async (
    page: number = 1,
    pageSizeParam: number = 20,
    forceRefresh: boolean = false,
    filters?: ChapterFilters
  ) => {
    const filterKey = filters ? `${filters.enhanced_only || false}-${filters.scraped_only || false}` : 'none';
    const cacheKey = `${storyId}-${page}-${pageSizeParam}-${filterKey}`;
    const cachedEntry = chaptersCache.get(cacheKey);

    if (cachedEntry && !forceRefresh) {
      // Force new array reference to trigger useEffect dependencies
      setChapters([...cachedEntry.chapters]);
      setTotalChapters(cachedEntry.totalChapters);
      setPageSize(pageSizeParam);
      setTotalPages(Math.ceil(cachedEntry.totalChapters / pageSizeParam));
      
      if (!isCacheValid(cachedEntry)) {
        revalidate(page, pageSizeParam, filters);
      }
      return cachedEntry;
    }

    setLoading(true);
    setError('');

    const [error, response] = await safeAsync(async () => {
      // Use network optimization for the request
      return await PerformanceMonitor.monitorRefresh(
        () => NetworkOptimizer.deduplicateRequest(
          cacheKey,
          () => NetworkOptimizer.throttleRequest(
            () => fetchChaptersByStoryId(storyId, page, pageSizeParam, filters, {
              disableCache: forceRefresh
            })
          )
        ),
        'loadChapters'
      );
    });

    if (error) {
      const componentError = createComponentError(
        error,
        'useChaptersCache',
        'loadChapters'
      );
      handleError(componentError);
      const errorMessage = getUserFriendlyMessage(normalizeError(error as ErrorLike));
      setError(errorMessage);
      setLoading(false);
      throw error;
    }

    if (!response) {
      throw new Error('No response received');
    }

    const newEntry: ChaptersCacheEntry = {
      chapters: response.data,
      totalChapters: response.pagination.total_items,
      lastFetched: Date.now(),
      isComplete: !response.pagination.has_next
    };

    chaptersCache.set(cacheKey, newEntry);
    
    // Setting new chapters data
    
    // Force new array reference to trigger useEffect dependencies
    setChapters([...response.data]);
    setTotalChapters(response.pagination.total_items);
    setPageSize(pageSizeParam);
    setTotalPages(Math.ceil(response.pagination.total_items / pageSizeParam));
    
    setLoading(false);
    
    // State updated successfully
    
    // Force a re-render check
    setTimeout(() => {
      // State verification completed
    }, 100);
    return newEntry;
  }, [storyId, isCacheValid, revalidate]);

  const goToPage = useCallback(async (page: number, filters?: ChapterFilters, customPageSize?: number) => {
    const effectivePageSize = customPageSize || pageSize;
    await loadChapters(page, effectivePageSize, false, filters);
  }, [loadChapters, pageSize]);

  const clearCache = useCallback(() => {
    // Clearing ALL cache entries for story
    
    // Clear all cache entries for this story (all pages, filters, etc.)
    Array.from(chaptersCache.keys()).forEach(key => {
      if (key.startsWith(storyId)) {
        chaptersCache.delete(key);
      }
    });
    
    // Also clear browser cache for this story's API endpoints
    if (typeof window !== 'undefined' && 'caches' in window) {
      caches.open('api-cache').then(cache => {
        cache.keys().then(requests => {
          requests.forEach(request => {
            if (request.url.includes(`/stories/${storyId}/chapters`)) {
              cache.delete(request);
            }
          });
        });
      }).catch(err => {
        // Browser cache clearing failed
      });
    }
    
    // Cache clearing completed
  }, [storyId]);

  const refreshChapters = useCallback(async (filters?: ChapterFilters, targetPage?: number) => {
    const pageToRefresh = targetPage || 1;
    // Starting refresh chapters for story
    
    return await PerformanceMonitor.monitorRefresh(async () => {
      try {
        // Check if we're offline
        if (!navigator.onLine) {
          const offlineError = {
            message: 'Cannot refresh chapters while offline',
            code: 'OFFLINE_ERROR',
            timestamp: new Date(),
            context: { offline: true }
          };
          setError('You are currently offline. Please check your internet connection and try again.');
          return { success: false, error: 'You are currently offline. Please check your internet connection and try again.' };
        }

        // Clearing cache for story
        clearCache();
        
        // About to load chapters with refresh
        // Reload chapters with current page and settings, forcing refresh
        // Use retry mechanism for network failures with connection-aware optimization
        const refreshOperation = async () => {
          // Starting loadChapters operation
          try {
            const result = await loadChapters(pageToRefresh, pageSize, true, filters);
            // loadChapters completed successfully
            return result;
          } catch (error) {
            // loadChapters failed
            throw error;
          }
        };
        
        await NetworkOptimizer.optimizeForConnection(
          // Fast connection: use normal retry
          async () => await retryWithBackoff(refreshOperation, {
            maxRetries: 2,
            baseDelay: 1000,
            maxDelay: 5000,
            shouldRetry: (error) => {
              return isNetworkError(error) || 
                     (isApiError(error) && error.status >= 500) ||
                     error.message.includes('fetch') ||
                     error.message.includes('network') ||
                     error.message.includes('timeout');
            }
          }),
          // Slow connection: use more conservative retry
          async () => await retryWithBackoff(refreshOperation, {
            maxRetries: 1,
            baseDelay: 2000,
            maxDelay: 8000,
            shouldRetry: (error) => {
              return isNetworkError(error) || 
                     (isApiError(error) && error.status >= 500);
            }
          })
        );
        
        // Refresh completed successfully
        return { success: true };
      } catch (error) {
      const normalizedError = normalizeError(error as ErrorLike);
      
      // Create specific error messages based on error type
      let userMessage = 'Failed to refresh chapters';
      
      if (isNetworkError(normalizedError)) {
        if (normalizedError.timeout) {
          userMessage = 'Request timed out. Please check your internet connection and try again.';
        } else if (normalizedError.offline) {
          userMessage = 'You appear to be offline. Please check your internet connection.';
        } else {
          userMessage = 'Network error occurred. Please check your internet connection and try again.';
        }
      } else if (isApiError(normalizedError)) {
        if (normalizedError.status >= 500) {
          userMessage = 'Server error occurred. Please try again in a few moments.';
        } else if (normalizedError.status === 404) {
          userMessage = 'Story not found. It may have been removed or moved.';
        } else if (normalizedError.status === 403) {
          userMessage = 'Access denied. You may need to log in again.';
        } else {
          userMessage = `Server responded with error: ${normalizedError.statusText || 'Unknown error'}`;
        }
      } else if (normalizedError.message.includes('fetch')) {
        userMessage = 'Failed to connect to server. Please check your internet connection.';
      }
      
      const componentError = createComponentError(
        error instanceof Error ? error : new Error(userMessage),
        'useChaptersCache',
        'refreshChapters'
      );
      
      // Log error for debugging but don't show technical details to user
      handleError(componentError, {
        showToast: false, // We'll handle toast in the calling component
        logError: true,
        context: {
          storyId,
          pageSize,
          filters,
          online: navigator.onLine
        }
      });
      
      setError(userMessage);
      
      return { success: false, error: userMessage };
       }
     }, 'refreshChapters');
   }, [pageSize, clearCache, loadChapters, storyId]);

  useEffect(() => {
    if (!initialChapters && chapters.length === 0 && !loading) {
      loadChapters();
    }
  }, [storyId, chapters.length, loading, loadChapters, initialChapters]);

  return {
    chapters,
    loading,
    error,
    totalChapters,
    totalPages,
    pageSize,
    loadChapters,
    goToPage,
    clearCache,
    refreshChapters,
    setPageSize,
    revalidate
  };
};

export default useChaptersCache;