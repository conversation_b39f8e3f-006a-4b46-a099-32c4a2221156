'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  ScrapeProgressUpdate,
  scrapingService
} from '@/services/scrapingService';
import { toast } from 'sonner';
import { 
  enhancementService, 
  EnhancementProgress,
  BatchEnhancementResponse,
  createBatchEnhancementRequest
} from '@/services/enhancementService';
import { createComponentError, handleError, safeAsync, getUserFriendlyMessage, normalizeError } from '@/utils/errorHandling';
import { 
  createBatchScrapeSuccessToast,
  createBatchEnhanceSuccessToast,
  createBatchErrorToast,
  createBatchLoadingToast,
  createBatchCancelledToast
} from '@/utils/toastHelpers';
import { ErrorLike } from '@/types/errors';

interface BatchScrapingState {
  isActive: boolean;
  jobId?: string;
  progress: ScrapeProgressUpdate | null;
}

interface BatchEnhancementState {
  isActive: boolean;
  jobId?: string;
  progress: EnhancementProgress | null;
}

interface UseBatchActionsOptions {
  onComplete: () => void;
  getOnRefresh?: () => (() => void) | undefined;
}

export const useBatchActions = (options: UseBatchActionsOptions | (() => void)) => {
  // Support both old and new API for backward compatibility
  const { onComplete, getOnRefresh } = typeof options === 'function'
    ? { onComplete: options, getOnRefresh: undefined }
    : options;

  const [batchScrapingState, setBatchScrapingState] = useState<BatchScrapingState>({ isActive: false, progress: null });
  const [batchEnhancementState, setBatchEnhancementState] = useState<BatchEnhancementState>({ isActive: false, progress: null });
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const clearPolling = () => {
    if (pollingIntervalRef.current) {
      // Clearing existing polling interval
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  useEffect(() => {
    return () => {
      // Clearing polling interval on unmount
      clearPolling();
      enhancementService.cleanup();
    };
  }, []);

  // Job polling function for batch scraping
  const startJobPolling = useCallback(async (jobId: string, toastId?: string | number) => {
    clearPolling(); // Clear any existing polling before starting a new one
    // Starting job polling

    // Biến để theo dõi xem đã hiển thị toast success hay chưa
    let successToastShown = false;

    const newIntervalId = setInterval(async () => {
      // Polling cycle started

      try {
        // Checking job status
        let jobStatus;
        try {
          jobStatus = await scrapingService.getJobStatus(jobId);
          // Job status response received
        } catch (error) {
          if (error instanceof SyntaxError) {
            // Failed to parse JSON response from getJobStatus
            // Attempt to get the raw text to see what's wrong
            try {
              const rawResponse = await scrapingService.getJobStatus(jobId, { parseAsText: true });
              // Raw response retrieved
            } catch (textError) {
              // Could not retrieve raw text response
            }
          }
          throw error; // Re-throw the original error to be caught by the outer block
        }
        
        if (typeof jobStatus === 'object' && jobStatus !== null && 'status' in jobStatus) {
          if (jobStatus.status === 'completed' && !successToastShown) {
            // Job completed successfully
            clearPolling();
            
            // Đánh dấu đã hiển thị toast success
            successToastShown = true;
            
            // Dismiss loading toast
            if (toastId) toast.dismiss(toastId);
            
            // Show completion toast with refresh button
            createBatchScrapeSuccessToast(
              () => {
                const onRefresh = getOnRefresh?.();
                if (onRefresh) {
                  // Calling onRefresh from toast action
                  onRefresh();
                }
              },
              `${jobStatus.completed_items || 0} chapters scraped successfully!`
            );
            
            // Remove duplicate onRefresh call to prevent duplicate actions
            // onRefresh is already handled in the toast button callback above
            
            setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
          } else if ((jobStatus.status === 'error' || jobStatus.status === 'cancelled') && !successToastShown) {
            // Job failed or was cancelled
            clearPolling();
            
            // Dismiss loading toast
            if (toastId) toast.dismiss(toastId);
            
            // Show error toast
            createBatchErrorToast(
              jobStatus.message || 'Batch scraping failed',
              'scraping'
            );
            
            setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
          } else if (jobStatus.status !== 'completed' && jobStatus.status !== 'error' && jobStatus.status !== 'cancelled') {
            // Job is still in progress (pending, in_progress, etc.)
            // Continue polling - don't clear interval
          }
        } else {
          // Invalid job status response - continue polling
        }
        
      } catch (error) {
        // Error polling for job
        
        // Check if this is a critical error that should stop polling
        if (error instanceof TypeError || (error as any)?.status === 404) {
          // Critical errors: stop polling
          clearPolling();
          setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
        } else {
          // Non-critical errors (network issues, temporary server errors): continue polling
        }
      }
    }, 3000); // Poll every 3 seconds
    
    // Store interval ID immediately after creation
    pollingIntervalRef.current = newIntervalId;
    // New polling interval created
  }, [getOnRefresh, clearPolling]);

  const handleBatchScrape = useCallback(async (storyId: string, chapterIds: string[]) => {
    // Starting batch scrape operation
    
    if (chapterIds.length === 0) {
      toast.warning('No chapters selected', { description: 'Please select at least one chapter to scrape.' });
      return;
    }

    // Chỉ tạo một job duy nhất cho tất cả chapters
    setBatchScrapingState({ isActive: true, progress: null });
    const progressToastId = createBatchLoadingToast('scraping', `Scraping ${chapterIds.length} chapters...`);
    // Loading toast created

    const [error, result] = await safeAsync(async () => {
      // Making API call to scrapingService.batchScrapeChapters
      const response = await scrapingService.batchScrapeChapters({
        story_id: storyId,
        chapter_ids: chapterIds
      });
      // API response received
      return response;
    });

    if (error) {
      // Error during batch scrape API call
      const componentError = createComponentError(
        error,
        'useBatchActions',
        'handleBatchScrape'
      );
      handleError(componentError);
      if (progressToastId) toast.dismiss(progressToastId);
      createBatchErrorToast(
        getUserFriendlyMessage(normalizeError(error as ErrorLike)),
        'scraping'
      );
      setBatchScrapingState({ isActive: false, progress: null });
      return;
    }

    if (result && result.success) {
        // Batch scrape initiated successfully
        if (progressToastId) toast.dismiss(progressToastId);
        // Initial loading toast dismissed
        
        // Create a new loading toast for polling phase
        const pollingToastId = createBatchLoadingToast('scraping', `Processing ${result.total_chapters_to_scrape} chapters...`);
        // Polling loading toast created
        
        // Store job_id for potential cancellation and start polling
        setBatchScrapingState({ isActive: true, progress: null, jobId: result.job_id });
        // Starting job polling
        
        // Start polling for job completion with the new toast ID
        startJobPolling(result.job_id, pollingToastId);
        onComplete();
      } else if (result) {
        // Batch scrape API call failed
        if (progressToastId) toast.dismiss(progressToastId);
        createBatchErrorToast(
          result.message || 'Failed to start batch scraping',
          'scraping'
        );
        setBatchScrapingState({ isActive: false, progress: null });
    }
  }, [onComplete, getOnRefresh, startJobPolling]);

  const handleBatchEnhance = useCallback(async (storyId: string, chapterIds: string[]) => {
    // Starting batch enhance operation
    
    if (chapterIds.length === 0) {
      toast.warning('No chapters selected', { description: 'Please select at least one chapter to enhance.' });
      return;
    }

    setBatchEnhancementState({ isActive: true, progress: null });
    const progressToastId = createBatchLoadingToast('enhancement', `Enhancing ${chapterIds.length} chapters...`);
    // Enhancement loading toast created

    const [error, result] = await safeAsync(async () => {
      const request = createBatchEnhancementRequest(chapterIds);
      return await enhancementService.batchEnhanceWithProgress(request, {
        onProgress: (progress: EnhancementProgress) => {
          // Enhancement progress update
          setBatchEnhancementState(prev => ({ ...prev, progress }));
          
          // Throttle toast updates to avoid UI conflicts
          if (progress.completed_items % 2 === 0 || progress.completed_items === progress.total_chapters) {
            toast.loading(`Enhancing: ${progress.completed_items}/${progress.total_chapters} chapters done...`, { 
              id: progressToastId,
              description: `Current chapter: ${progress.current_chapter || 'Processing...'}`
            });
          }
        },
        onComplete: (result: BatchEnhancementResponse) => {
            // Enhancement completed
            if (progressToastId) toast.dismiss(progressToastId);
            // Enhancement loading toast dismissed
            
            // Show enhanced success toast with refresh button
            createBatchEnhanceSuccessToast(
              () => {
                const onRefresh = getOnRefresh?.();
                if (onRefresh) {
                  // Calling onRefresh from toast action
                  onRefresh();
                }
              },
              `${result.completed_chapters} chapters were successfully enhanced.`
            );

            // Remove duplicate onRefresh call to prevent duplicate actions
            // onRefresh is already handled in the toast button callback above
            
            setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
            // Enhancement onComplete called
            onComplete();
        },
        onError: (error: Error) => {
            // Enhancement error occurred
            const componentError = createComponentError(
              error,
              'useBatchActions',
              'handleBatchEnhance'
            );
            handleError(componentError);
            if (progressToastId) toast.dismiss(progressToastId);
            // Enhancement error - loading toast dismissed
            createBatchErrorToast(
              getUserFriendlyMessage(normalizeError(error as ErrorLike)),
              'enhancement'
            );
            setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
            // Enhancement error state reset
        }
      });
    });

    if (error) {
      const componentError = createComponentError(
        error,
        'useBatchActions',
        'handleBatchEnhance'
      );
      handleError(componentError);
      if (progressToastId) toast.dismiss(progressToastId);
      createBatchErrorToast(
        getUserFriendlyMessage(normalizeError(error as ErrorLike)),
        'enhancement'
      );
      setBatchEnhancementState({ isActive: false, progress: null });
    } else if (result) {
      setBatchEnhancementState(prev => ({ ...prev, jobId: result as string }));
    }
  }, [onComplete, getOnRefresh]);

  const cancelAction = (jobId?: string) => {
    try {
      if (!jobId) return;
      
      if (batchScrapingState.isActive && batchScrapingState.jobId) {
          // Cancel scraping job if needed
          setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
          createBatchCancelledToast('scraping', 'The scraping process has been stopped.');
      } else if (batchEnhancementState.isActive && batchEnhancementState.jobId) {
          enhancementService.cancelEnhancementJob(batchEnhancementState.jobId);
          setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
          createBatchCancelledToast('enhancement', 'The enhancement process has been stopped.');
      }
    } catch (error) {
      const componentError = createComponentError(
        error instanceof Error ? error : new Error('Cancel action failed'),
        'useBatchActions',
        'cancelAction'
      );
      handleError(componentError);
      toast.error('Failed to cancel action', { 
        description: getUserFriendlyMessage(normalizeError((error instanceof Error ? error : new Error('Unknown error')) as ErrorLike)) 
      });
    }
  };

  return {
    batchScrapingState,
    batchEnhancementState,
    handleBatchScrape,
    handleBatchEnhance,
    cancelAction,
  };
};
