import React from 'react';

/**
 * Utility functions for chapter processing and display
 */

/**
 * Cleans up chapter titles by removing redundant prefixes and formatting
 * @param title - The original chapter title
 * @returns Cleaned chapter title
 */
export const cleanChapterTitle = (title: string | undefined): string => {
  if (!title || title.trim() === '') {
    return '';
  }

  let cleanedTitle = title.trim();

  // Remove common redundant prefixes
  const prefixPatterns = [
    /^(Chapter|Chương)\s*\d+[:\s,]*\s*/i,
    /^Chapter\s*\d+[:\s,]*\s*/i,
    /^Ch\s*\d+[:\s,]*\s*/i,
    /^\d+[:\s,]*\s*/,
  ];

  // Apply each pattern to remove redundant prefixes
  for (const pattern of prefixPatterns) {
    cleanedTitle = cleanedTitle.replace(pattern, '');
  }

  // Remove multiple consecutive spaces and trim
  cleanedTitle = cleanedTitle.replace(/\s+/g, ' ').trim();

  // If the cleaned title is empty or just punctuation, return empty string
  if (!cleanedTitle || /^[:\s,.-]*$/.test(cleanedTitle)) {
    return '';
  }

  return cleanedTitle;
};

/**
 * Formats chapter display name with number and cleaned title
 * @param chapterNumber - The chapter number
 * @param title - The original chapter title
 * @returns Formatted chapter display name
 */
export const formatChapterDisplayName = (chapterNumber: number, title?: string): string => {
  const cleanedTitle = cleanChapterTitle(title);
  
  if (cleanedTitle) {
    return `Chapter ${chapterNumber}: ${cleanedTitle}`;
  }
  
  return `Chapter ${chapterNumber}`;
};

/**
 * Gets the status badge configuration for a chapter
 * @param chapter - The chapter object
 * @returns Status badge configuration
 */
export const getChapterStatusBadge = (chapter: { is_enhanced: boolean; is_scraped: boolean }) => {
  // Return icon-only indicators for cleaner UI
  if (chapter.is_enhanced) {
    return (
      <div 
        className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full" 
        title="Enhanced"
      >
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      </div>
    );
  } else if (chapter.is_scraped) {
    return (
      <div 
        className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full" 
        title="Scraped"
      >
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </div>
    );
  } else {
    return (
      <div 
        className="flex items-center justify-center w-6 h-6 bg-gray-500 rounded-full" 
        title="Not Scraped"
      >
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
      </div>
    );
  }
};