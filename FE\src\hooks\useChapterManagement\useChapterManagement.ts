'use client';

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';

import { useChaptersCache } from '@/hooks/useChaptersCache';
import { usePageContextSafe } from '@/components/story/StoryDetailWrapper';
import { Chapter } from '@/types/story';

export interface ChapterFilter {
  scraped?: boolean | null;
  enhanced?: boolean | null;
  search?: string;
}

export const useChapterManagement = (storyId: string, initialChapters?: Chapter[], refreshKey?: number) => {
  const {
    chapters,
    loading: chaptersLoading,
    totalChapters,
    totalPages,
    pageSize,
    setPageSize,
    goToPage,
    refreshChapters
  } = useChaptersCache(storyId, initialChapters);
  
  // Debug: Track chapters changes from useChaptersCache
  useEffect(() => {
    // Chapters tracking from useChaptersCache
  }, [chapters, chaptersLoading, storyId, refreshKey]);
  
  // Debug: Simple render tracking for useChapterManagement
  // Render tracking removed
  
  // Use shared page context instead of cache's internal currentPage
  const { currentPage, setCurrentPage } = usePageContextSafe();

  const [selectedChapters, setSelectedChapters] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<ChapterFilter>({ scraped: null, enhanced: null });

  // Force re-fetch when refreshKey changes
  useEffect(() => {
    if (refreshKey && refreshKey > 0) {
      // RefreshKey changed, forcing chapter reload
      // Clear any local state that might be stale
      setSelectedChapters(new Set());
      setFilter({ scraped: null, enhanced: null });
      
      // Force reload the current page to ensure UI reflects the latest data
      goToPage(currentPage).catch(error => {
        // Failed to reload current page
      });
    }
  }, [refreshKey, currentPage, goToPage]);
  const [paginationLoading, setPaginationLoading] = useState(false);

  // Track if this is a user-initiated pagination change
  const isUserPaginationRef = useRef(false);

  // Load chapters with filters when filter changes (excluding search)
  // Only reset to page 1 if it's not a user pagination action
  useEffect(() => {
    if (isUserPaginationRef.current) {
      // Reset the flag and don't reset to page 1
      isUserPaginationRef.current = false;
      return;
    }
    
    // Filter changed, resetting to page 1
    const apiFilters = {
      enhanced_only: filter.enhanced === true ? true : undefined,
      scraped_only: filter.scraped === true ? true : undefined
    };
    goToPage(1, apiFilters);
  }, [filter.scraped, filter.enhanced, storyId, goToPage]);

  const filteredChapters = useMemo(() => {
    if (!chapters) return [];

    let filtered = chapters;

    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = chapters.filter(
        (chapter) =>
          (chapter.title && chapter.title.toLowerCase().includes(searchTerm)) ||
          chapter.chapter_number.toString().includes(searchTerm)
      );
    }

    return filtered.sort((a, b) => a.chapter_number - b.chapter_number);
  }, [chapters, filter.search, refreshKey]); // Add refreshKey to force re-computation

  const chapterStats = useMemo(() => {
    const total = totalChapters;
    const scraped = chapters.filter((ch) => ch.is_scraped).length;
    const enhanced = chapters.filter((ch) => ch.is_enhanced).length;
    const unscraped = total - scraped;
    const scrapedNotEnhanced = scraped - enhanced;
    const selected = selectedChapters.size;

    return { total, scraped, enhanced, unscraped, scrapedNotEnhanced, selected };
  }, [chapters, totalChapters, selectedChapters, refreshKey]); // Add refreshKey to force re-computation

  // Reset selections when filter changes
  useEffect(() => {
    setSelectedChapters(new Set());
  }, [filter]);

  const handleSelectChapter = useCallback((chapterId: string) => {
    setSelectedChapters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(chapterId)) {
        newSet.delete(chapterId);
      } else {
        newSet.add(chapterId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((mode: 'scraping' | 'enhancement') => {
    let selectableChapters: string[] = [];

    if (mode === 'scraping') {
      selectableChapters = chapters.filter((ch) => !ch.is_scraped).map((ch) => ch.id);
    } else if (mode === 'enhancement') {
      selectableChapters = chapters.filter((ch) => ch.is_scraped && !ch.is_enhanced).map((ch) => ch.id);
    }

    setSelectedChapters(new Set(selectableChapters));
  }, [chapters]);

  const handleDeselectAll = useCallback(() => {
    setSelectedChapters(new Set());
  }, []);

  // Handle page change with current filters
  const handlePageChange = useCallback(async (page: number) => {
    // Page change requested
    
    // Set flag to prevent useEffect from resetting to page 1
    isUserPaginationRef.current = true;
    
    // Update shared page context immediately
    setCurrentPage(page);
    
    // Set pagination loading state
    setPaginationLoading(true);
    
    try {
      const apiFilters = {
        enhanced_only: filter.enhanced === true ? true : undefined,
        scraped_only: filter.scraped === true ? true : undefined
      };
      await goToPage(page, apiFilters);
    } finally {
      // Clear loading state after a short delay to show loading feedback
      setTimeout(() => {
        setPaginationLoading(false);
      }, 300);
    }
  }, [goToPage, filter.enhanced, filter.scraped, currentPage, setCurrentPage]);



  return {
    chapters,
    chaptersLoading,
    totalChapters,
    filteredChapters,
    currentPage,
    totalPages,
    pageSize,
    setPageSize,
    filter,
    setFilter,
    selectedChapters,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
    handlePageChange,
    chapterStats,
    paginationLoading,
  };
};
