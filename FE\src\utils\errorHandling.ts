/**
 * Error Handling Utilities for WebTruyen Application
 * Provides consistent error processing and reporting functions
 */

import { toast } from 'sonner';
import {
  BaseError,
  ApiError,
  AppError,
  ComponentError,
  NetworkError,
  ValidationError,
  AppErrorType,
  ErrorSeverity,
  ErrorContext,
  ErrorReport,
  ErrorLike,
  ErrorResult,
  isApiError,
  isAppError,
  isComponentError,
  isNetworkError,
  isValidationError
} from '@/types/errors';

/**
 * Normalizes any error-like object into a BaseError
 */
export function normalizeError(error: ErrorLike, context?: Partial<ErrorContext>): BaseError {
  const timestamp = new Date();
  
  if (typeof error === 'string') {
    return {
      message: error,
      timestamp,
      context
    };
  }
  
  if (error instanceof Error) {
    return {
      message: error.message,
      code: error.name,
      timestamp,
      context: {
        ...context,
        stack: error.stack
      }
    };
  }
  
  if (isApiError(error) || isAppError(error) || isComponentError(error)) {
    return {
      ...error,
      timestamp: error.timestamp || timestamp,
      context: {
        ...error.context,
        ...context
      }
    };
  }
  
  return {
    message: 'An unknown error occurred',
    code: 'UNKNOWN_ERROR',
    timestamp,
    context
  };
}

/**
 * Creates an ApiError from a fetch Response
 */
export async function createApiError(
  response: Response,
  endpoint?: string,
  method?: string
): Promise<ApiError> {
  let message = `HTTP ${response.status}: ${response.statusText}`;
  let code = `HTTP_${response.status}`;
  
  try {
    const errorData = await response.json();
    if (errorData.error?.message) {
      message = errorData.error.message;
    }
    if (errorData.error?.code) {
      code = errorData.error.code;
    }
  } catch {
    // Fallback to status text if response is not JSON
  }
  
  return {
    message,
    code,
    status: response.status,
    statusText: response.statusText,
    endpoint,
    method,
    timestamp: new Date()
  };
}

/**
 * Creates an AppError with proper typing
 */
export function createAppError(
  message: string,
  type: AppErrorType = AppErrorType.UNKNOWN,
  recoverable: boolean = true,
  userMessage?: string,
  context?: Record<string, unknown>
): AppError {
  return {
    message,
    type,
    recoverable,
    userMessage: userMessage || message,
    timestamp: new Date(),
    context
  };
}

/**
 * Creates a ComponentError for error boundaries
 */
export function createComponentError(
  error: Error,
  componentName: string,
  errorBoundary?: string
): ComponentError {
  return {
    message: error.message,
    code: error.name,
    componentName,
    componentStack: error.stack,
    errorBoundary,
    timestamp: new Date(),
    context: {
      originalError: error
    }
  };
}

/**
 * Creates a ValidationError for form validation
 */
export function createValidationError(
  field: string,
  value: unknown,
  constraint: string,
  message?: string
): ValidationError {
  return {
    message: message || `Validation failed for ${field}: ${constraint}`,
    code: 'VALIDATION_ERROR',
    field,
    value,
    constraint,
    timestamp: new Date()
  };
}

/**
 * Gets user-friendly error message
 */
export function getUserFriendlyMessage(error: BaseError): string {
  if (isAppError(error) && error.userMessage) {
    return error.userMessage;
  }
  
  if (isApiError(error)) {
    switch (error.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'You need to log in to access this feature.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 503:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return 'An error occurred while processing your request.';
    }
  }
  
  if (isNetworkError(error)) {
    if (error.timeout) {
      return 'Request timed out. Please check your connection and try again.';
    }
    if (error.offline) {
      return 'You appear to be offline. Please check your connection.';
    }
    return 'Network error. Please check your connection and try again.';
  }
  
  if (isValidationError(error)) {
    return `Invalid ${error.field}: ${error.constraint}`;
  }
  
  return error.message || 'An unexpected error occurred.';
}

/**
 * Determines error severity
 */
export function getErrorSeverity(error: BaseError): ErrorSeverity {
  if (isApiError(error)) {
    if (error.status >= 500) return ErrorSeverity.CRITICAL;
    if (error.status >= 400) return ErrorSeverity.MEDIUM;
    return ErrorSeverity.LOW;
  }
  
  if (isAppError(error)) {
    switch (error.type) {
      case AppErrorType.AUTHENTICATION:
      case AppErrorType.AUTHORIZATION:
        return ErrorSeverity.HIGH;
      case AppErrorType.VALIDATION:
        return ErrorSeverity.LOW;
      case AppErrorType.NETWORK:
      case AppErrorType.SERVER:
        return ErrorSeverity.MEDIUM;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }
  
  if (isComponentError(error)) {
    return ErrorSeverity.HIGH;
  }
  
  return ErrorSeverity.MEDIUM;
}

/**
 * Shows appropriate toast notification for error
 */
export function showErrorToast(error: BaseError, options?: {
  title?: string;
  duration?: number;
  action?: { label: string; onClick: () => void };
}) {
  const message = getUserFriendlyMessage(error);
  const severity = getErrorSeverity(error);
  
  const toastOptions = {
    duration: options?.duration || (severity === ErrorSeverity.CRITICAL ? 10000 : 5000),
    action: options?.action
  };
  
  toast.error(options?.title || 'Error', {
    description: message,
    ...toastOptions
  });
}

/**
 * Logs error with appropriate level
 */
export function logError(error: BaseError, context?: Record<string, unknown>) {
  const severity = getErrorSeverity(error);
  const logData = {
    error: {
      message: error.message,
      code: error.code,
      timestamp: error.timestamp
    },
    context: {
      ...error.context,
      ...context
    },
    severity
  };
  
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      // Critical error logging removed
      break;
    case ErrorSeverity.HIGH:
      // High severity error logging removed
      break;
    case ErrorSeverity.MEDIUM:
      // Medium severity error logging removed
      break;
    case ErrorSeverity.LOW:
      // Low severity error logging removed
      break;
  }
}

/**
 * Handles error with logging and user notification
 */
export function handleError(
  error: ErrorLike,
  options?: {
    showToast?: boolean;
    logError?: boolean;
    context?: Record<string, unknown>;
    toastOptions?: { title?: string; duration?: number; action?: { label: string; onClick: () => void } };
  }
) {
  const normalizedError = normalizeError(error, options?.context);
  
  if (options?.logError !== false) {
    logError(normalizedError, options?.context);
  }
  
  if (options?.showToast !== false) {
    showErrorToast(normalizedError, options?.toastOptions);
  }
  
  return normalizedError;
}

/**
 * Creates a safe async wrapper that handles errors
 */
export function withErrorHandling<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  errorHandler?: (error: BaseError) => void
) {
  return async (...args: T): Promise<ErrorResult<R>> => {
    try {
      const data = await fn(...args);
      return { success: true, data };
    } catch (error) {
      const normalizedError = normalizeError(error as ErrorLike);
      
      if (errorHandler) {
        errorHandler(normalizedError);
      } else {
        handleError(normalizedError);
      }
      
      return { success: false, error: normalizedError };
    }
  };
}

/**
 * Safe async function that returns a tuple [error, result]
 */
export async function safeAsync<T>(
  fn: () => Promise<T>
): Promise<[Error | null, T | null]> {
  try {
    const result = await fn();
    return [null, result];
  } catch (error) {
    return [error instanceof Error ? error : new Error(String(error)), null];
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  options: {
    maxRetries?: number;
    baseDelay?: number;
    maxDelay?: number;
    shouldRetry?: (error: BaseError) => boolean;
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    shouldRetry = (error) => isNetworkError(error) || (isApiError(error) && error.status >= 500)
  } = options;
  
  let lastError: BaseError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = normalizeError(error as ErrorLike);
      
      if (attempt === maxRetries || !shouldRetry(lastError)) {
        throw lastError;
      }
      
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}