'use client';

// ============================================================================
// Color Contrast Utilities for WCAG AA Compliance
// ============================================================================

/**
 * Convert hex color to RGB
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Convert RGB to relative luminance
 */
export function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Calculate contrast ratio between two colors
 */
export function getContrastRatio(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if color combination meets WCAG AA standards
 */
export function meetsWCAGAA(foreground: string, background: string, isLargeText = false): boolean {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
}

/**
 * Check if color combination meets WCAG AAA standards
 */
export function meetsWCAGAAA(foreground: string, background: string, isLargeText = false): boolean {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 4.5 : ratio >= 7;
}

/**
 * Get accessible color variants
 */
export interface ColorVariants {
  primary: string;
  secondary: string;
  muted: string;
  accent: string;
  destructive: string;
  success: string;
  warning: string;
}

/**
 * WCAG AA compliant color palettes
 */
export const accessibleColors = {
  light: {
    background: '#ffffff',
    foreground: '#0f172a',
    primary: '#2563eb',
    primaryForeground: '#ffffff',
    secondary: '#f1f5f9',
    secondaryForeground: '#0f172a',
    muted: '#f8fafc',
    mutedForeground: '#64748b',
    accent: '#f59e0b',
    accentForeground: '#ffffff',
    destructive: '#dc2626',
    destructiveForeground: '#ffffff',
    success: '#16a34a',
    successForeground: '#ffffff',
    warning: '#d97706',
    warningForeground: '#ffffff',
    border: '#e2e8f0',
    input: '#e2e8f0',
    ring: '#2563eb'
  },
  dark: {
    background: '#0f172a',
    foreground: '#f8fafc',
    primary: '#3b82f6',
    primaryForeground: '#ffffff',
    secondary: '#1e293b',
    secondaryForeground: '#f8fafc',
    muted: '#1e293b',
    mutedForeground: '#94a3b8',
    accent: '#f59e0b',
    accentForeground: '#0f172a',
    destructive: '#ef4444',
    destructiveForeground: '#ffffff',
    success: '#22c55e',
    successForeground: '#ffffff',
    warning: '#eab308',
    warningForeground: '#0f172a',
    border: '#334155',
    input: '#334155',
    ring: '#3b82f6'
  },
  highContrast: {
    background: '#000000',
    foreground: '#ffffff',
    primary: '#ffffff',
    primaryForeground: '#000000',
    secondary: '#333333',
    secondaryForeground: '#ffffff',
    muted: '#1a1a1a',
    mutedForeground: '#cccccc',
    accent: '#ffff00',
    accentForeground: '#000000',
    destructive: '#ff0000',
    destructiveForeground: '#ffffff',
    success: '#00ff00',
    successForeground: '#000000',
    warning: '#ffff00',
    warningForeground: '#000000',
    border: '#666666',
    input: '#333333',
    ring: '#ffffff'
  }
};

/**
 * Validate all color combinations in a theme
 */
export function validateThemeContrast(theme: Record<string, string>): {
  valid: boolean;
  issues: Array<{ pair: string; ratio: number; required: number }>;
} {
  const issues: Array<{ pair: string; ratio: number; required: number }> = [];
  
  const colorPairs = [
    { fg: 'foreground', bg: 'background', large: false },
    { fg: 'primaryForeground', bg: 'primary', large: false },
    { fg: 'secondaryForeground', bg: 'secondary', large: false },
    { fg: 'mutedForeground', bg: 'muted', large: false },
    { fg: 'accentForeground', bg: 'accent', large: false },
    { fg: 'destructiveForeground', bg: 'destructive', large: false },
    { fg: 'successForeground', bg: 'success', large: false },
    { fg: 'warningForeground', bg: 'warning', large: false }
  ];
  
  colorPairs.forEach(({ fg, bg, large }) => {
    if (theme[fg] && theme[bg]) {
      const ratio = getContrastRatio(theme[fg], theme[bg]);
      const required = large ? 3 : 4.5;
      
      if (ratio < required) {
        issues.push({
          pair: `${fg}/${bg}`,
          ratio: Math.round(ratio * 100) / 100,
          required
        });
      }
    }
  });
  
  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Generate accessible color based on background
 */
export function getAccessibleColor(background: string, preferDark = true): string {
  const rgb = hexToRgb(background);
  if (!rgb) return preferDark ? '#000000' : '#ffffff';
  
  const luminance = getLuminance(rgb.r, rgb.g, rgb.b);
  
  // If background is light, use dark text
  if (luminance > 0.5) {
    return '#000000';
  }
  
  // If background is dark, use light text
  return '#ffffff';
}

/**
 * Adjust color brightness to meet contrast requirements
 */
export function adjustColorForContrast(
  color: string,
  background: string,
  targetRatio = 4.5
): string {
  const colorRgb = hexToRgb(color);
  const bgRgb = hexToRgb(background);
  
  if (!colorRgb || !bgRgb) return color;
  
  let { r, g, b } = colorRgb;
  const bgLuminance = getLuminance(bgRgb.r, bgRgb.g, bgRgb.b);
  
  // Determine if we need to make the color lighter or darker
  const colorLuminance = getLuminance(r, g, b);
  const shouldLighten = colorLuminance < bgLuminance;
  
  let attempts = 0;
  const maxAttempts = 100;
  
  while (attempts < maxAttempts) {
    const currentRatio = getContrastRatio(
      `rgb(${r},${g},${b})`,
      `rgb(${bgRgb.r},${bgRgb.g},${bgRgb.b})`
    );
    
    if (currentRatio >= targetRatio) {
      break;
    }
    
    if (shouldLighten) {
      r = Math.min(255, r + 5);
      g = Math.min(255, g + 5);
      b = Math.min(255, b + 5);
    } else {
      r = Math.max(0, r - 5);
      g = Math.max(0, g - 5);
      b = Math.max(0, b - 5);
    }
    
    attempts++;
  }
  
  return `rgb(${r},${g},${b})`;
}

/**
 * CSS custom properties for accessible colors
 */
export function generateAccessibleCSSVariables(theme: 'light' | 'dark' | 'highContrast'): string {
  const colors = accessibleColors[theme];
  
  return Object.entries(colors)
    .map(([key, value]) => {
      const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      const rgb = hexToRgb(value);
      return rgb ? `--${cssVar}: ${rgb.r} ${rgb.g} ${rgb.b};` : '';
    })
    .filter(Boolean)
    .join('\n');
}

/**
 * Apply accessible theme to document
 */
export function applyAccessibleTheme(theme: 'light' | 'dark' | 'highContrast'): void {
  const root = document.documentElement;
  const colors = accessibleColors[theme];
  
  Object.entries(colors).forEach(([key, value]) => {
    const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
    const rgb = hexToRgb(value);
    if (rgb) {
      root.style.setProperty(`--${cssVar}`, `${rgb.r} ${rgb.g} ${rgb.b}`);
    }
  });
  
  // Add theme class for additional styling
  root.classList.remove('light', 'dark', 'high-contrast');
  root.classList.add(theme === 'highContrast' ? 'high-contrast' : theme);
}

/**
 * Check if user prefers high contrast
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Get system color scheme preference
 */
export function getSystemColorScheme(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

/**
 * Color contrast testing utilities
 */
export const contrastTesting = {
  /**
   * Test all color combinations in current theme
   */
  testCurrentTheme(): void {
    if (typeof window === 'undefined') return;
    
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    const colors = {
      background: computedStyle.getPropertyValue('--background').trim(),
      foreground: computedStyle.getPropertyValue('--foreground').trim(),
      primary: computedStyle.getPropertyValue('--primary').trim(),
      primaryForeground: computedStyle.getPropertyValue('--primary-foreground').trim(),
      // Add more as needed
    };
    
    // Color Contrast Analysis
    
    Object.entries(colors).forEach(([name, value]) => {
      if (value) {
        const rgb = value.split(' ').map(Number);
        if (rgb.length === 3) {
          const hex = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
          // Color logging removed
        }
      }
    });
    
    // Analysis complete
  },
  
  /**
   * Generate contrast report
   */
  generateReport(theme: Record<string, string>): void {
    const validation = validateThemeContrast(theme);
    
    // WCAG Contrast Report generation
    // Overall validation and issues tracking removed from console
    
    if (!validation.valid && validation.issues.length > 0) {
      // Issues tracking removed from console
      validation.issues.forEach(issue => {
        // Issue logging removed
      });
    }
  }
};