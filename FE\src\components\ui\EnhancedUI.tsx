'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Sparkles, X, Filter, ChevronDown, Search } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// ============================================================================
// Enhanced UI Components for Better UX
// ============================================================================

/**
 * Enhanced Hamburger Menu with smooth animations
 */
interface EnhancedHamburgerProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'rounded';
}

export const EnhancedHamburger: React.FC<EnhancedHamburgerProps> = ({
  isOpen,
  onToggle,
  className,
  size = 'md',
  variant = 'default'
}) => {
  const sizes = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-7 h-7'
  };

  const variants = {
    default: 'bg-zinc-800/50 hover:bg-zinc-700',
    minimal: 'bg-transparent hover:bg-zinc-800/30',
    rounded: 'bg-zinc-800/50 hover:bg-zinc-700 rounded-full'
  };

  return (
    <motion.button
      onClick={onToggle}
      type="button"
      className={cn(
        'inline-flex items-center justify-center min-h-touch-target min-w-touch-target rounded-md text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-all duration-200 touch-manipulation',
        variants[variant],
        className
      )}
      aria-controls="mobile-menu"
      aria-expanded={isOpen}
      aria-label={isOpen ? 'Close main menu' : 'Open main menu'}
      whileTap={{ scale: 0.95 }}
      whileHover={{ scale: 1.05 }}
    >
      <div className={cn('relative', sizes[size])}>
        {/* Top line */}
        <motion.span
          className="absolute block h-0.5 w-full bg-current origin-center"
          animate={{
            rotate: isOpen ? 45 : 0,
            y: isOpen ? 0 : -6,
            opacity: 1
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        />
        
        {/* Middle line */}
        <motion.span
          className="absolute block h-0.5 w-full bg-current origin-center"
          animate={{
            opacity: isOpen ? 0 : 1,
            scale: isOpen ? 0 : 1
          }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
        />
        
        {/* Bottom line */}
        <motion.span
          className="absolute block h-0.5 w-full bg-current origin-center"
          animate={{
            rotate: isOpen ? -45 : 0,
            y: isOpen ? 0 : 6,
            opacity: 1
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        />
      </div>
    </motion.button>
  );
};

/**
 * Enhanced Filter Controls with improved UX
 */
interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

interface EnhancedFilterControlsProps {
  title?: string;
  filters: {
    scraped: FilterOption[];
    enhanced: FilterOption[];
  };
  values: {
    scraped: string;
    enhanced: string;
  };
  onChange: (type: 'scraped' | 'enhanced', value: string) => void;
  onClear: () => void;
  className?: string;
  compact?: boolean;
}

export const EnhancedFilterControls: React.FC<EnhancedFilterControlsProps> = ({
  title = 'Filter',
  filters,
  values,
  onChange,
  onClear,
  className,
  compact = false
}) => {
  const [isExpanded, setIsExpanded] = useState(!compact);
  const hasActiveFilters = values.scraped !== 'all' || values.enhanced !== 'all';

  return (
    <motion.div 
      className={cn(
        'bg-zinc-800/50 rounded-lg border border-zinc-700/50 overflow-hidden',
        className
      )}
      layout
    >
      {/* Header */}
      {compact && (
        <motion.button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full p-3 flex items-center justify-between text-left hover:bg-zinc-700/30 transition-colors"
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">{title}</span>
            {hasActiveFilters && (
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 text-xs">
                Active
              </Badge>
            )}
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </motion.div>
        </motion.button>
      )}

      {/* Filter Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="p-3 space-y-3">
              {!compact && (
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-300">{title}:</h3>
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 text-xs">
                      {Object.values(values).filter(v => v !== 'all').length} active
                    </Badge>
                  )}
                </div>
              )}
              
              {/* Filter Controls */}
              <div className="flex flex-col xs:flex-row items-stretch xs:items-center gap-3">
                {/* Scraped Filter */}
                <div className="flex items-center gap-2 flex-1">
                  <label className="text-xs text-gray-400 min-w-0 flex-shrink-0">
                    Scrape:
                  </label>
                  <select
                    value={values.scraped}
                    onChange={(e) => onChange('scraped', e.target.value)}
                    className="flex-1 px-3 py-2 text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all touch-manipulation min-h-touch-target"
                    aria-label="Filter by scrape status"
                  >
                    {filters.scraped.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.value === 'all' ? option.label : `${option.label} (${option.count})`}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Enhanced Filter */}
                <div className="flex items-center gap-2 flex-1">
                  <label className="text-xs text-gray-400 min-w-0 flex-shrink-0">
                    Enhance:
                  </label>
                  <select
                    value={values.enhanced}
                    onChange={(e) => onChange('enhanced', e.target.value)}
                    className="flex-1 px-3 py-2 text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all touch-manipulation min-h-touch-target"
                    aria-label="Filter by enhancement status"
                  >
                    {filters.enhanced.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.value === 'all' ? option.label : `${option.label} (${option.count})`}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Clear Button */}
                <motion.button
                  onClick={onClear}
                  disabled={!hasActiveFilters}
                  className={cn(
                    'px-3 py-2 text-sm rounded transition-all touch-manipulation min-h-touch-target',
                    hasActiveFilters
                      ? 'bg-zinc-600 hover:bg-zinc-500 active:bg-zinc-400 text-white'
                      : 'bg-zinc-700/50 text-gray-500 cursor-not-allowed'
                  )}
                  whileTap={hasActiveFilters ? { scale: 0.95 } : {}}
                  aria-label="Clear all filters"
                >
                  Clear
                </motion.button>
              </div>

              {/* Active Filters Display */}
              <AnimatePresence>
                {hasActiveFilters && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex flex-wrap gap-2 pt-2 border-t border-zinc-700/50"
                  >
                    {values.scraped !== 'all' && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0 }}
                        className="inline-flex items-center px-2 py-1 text-xs bg-green-900/30 text-green-400 rounded"
                      >
                        Scraped: {filters.scraped.find(f => f.value === values.scraped)?.label}
                        <button
                          onClick={() => onChange('scraped', 'all')}
                          className="ml-1.5 text-green-300 hover:text-green-200 w-4 h-4 flex items-center justify-center"
                          aria-label="Remove scrape filter"
                        >
                          X
                        </button>
                      </motion.span>
                    )}
                    {values.enhanced !== 'all' && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0 }}
                        className="inline-flex items-center px-2 py-1 text-xs bg-blue-900/30 text-blue-400 rounded"
                      >
                        Enhanced: {filters.enhanced.find(f => f.value === values.enhanced)?.label}
                        <button
                          onClick={() => onChange('enhanced', 'all')}
                          className="ml-1.5 text-blue-300 hover:text-blue-200 w-4 h-4 flex items-center justify-center"
                          aria-label="Remove enhancement filter"
                        >
                          X
                        </button>
                      </motion.span>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

/**
 * Enhanced Action Buttons with improved states
 */
interface EnhancedActionButtonsProps {
  selectionMode: 'scraping' | 'enhancement' | null;
  onToggleScrapeMode: () => void;
  onToggleEnhanceMode: () => void;
  selectedCount?: number;
  scrapableCount?: number;
  enhancableCount?: number;
  className?: string;
}

export const EnhancedActionButtons: React.FC<EnhancedActionButtonsProps> = ({
  selectionMode,
  onToggleScrapeMode,
  onToggleEnhanceMode,
  selectedCount = 0,
  scrapableCount = 0,
  enhancableCount = 0,
  className
}) => {
  return (
    <div className={cn('flex items-center gap-2 sm:gap-3', className)}>
      {/* Scrape Button */}
      <motion.div className="flex-1 xs:flex-none">
        <motion.button
          onClick={onToggleScrapeMode}
          className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full gap-1.5 sm:gap-2 min-h-touch-target touch-manipulation text-sm sm:text-base relative h-9 px-3 ${
            selectionMode === 'scraping' 
              ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
              : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
          }`}
          whileTap={{ scale: 0.95 }}
        >
          <motion.div
            animate={{
              rotate: selectionMode === 'scraping' ? 180 : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {selectionMode === 'scraping' ? (
              <X className="h-3.5 w-3.5 sm:h-4 sm:w-4" aria-hidden="true" />
            ) : (
              <Download className="h-3.5 w-3.5 sm:h-4 sm:w-4" aria-hidden="true" />
            )}
          </motion.div>
          
          <span className="hidden xs:inline">
            {selectionMode === 'scraping' ? 'Cancel' : 'Scrape'}
          </span>
          <span className="xs:hidden">
            {selectionMode === 'scraping' ? 'Cancel Selection' : 'Scrape Chapters'}
          </span>
          
          {/* Count Badge */}
          <AnimatePresence>
            {((selectionMode === 'scraping' && selectedCount > 0) || (selectionMode !== 'scraping' && scrapableCount > 0)) && (
              <motion.span
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center z-10 shadow-lg"
              >
                {selectionMode === 'scraping' ? selectedCount : scrapableCount}
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
      </motion.div>

      {/* Enhance Button */}
      <motion.div className="flex-1 xs:flex-none">
        <motion.button
          onClick={onToggleEnhanceMode}
          className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full gap-1.5 sm:gap-2 min-h-touch-target touch-manipulation text-sm sm:text-base relative h-9 px-3 ${
            selectionMode === 'enhancement' 
              ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
              : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
          }`}
          whileTap={{ scale: 0.95 }}
        >
          <motion.div
            animate={{
              rotate: selectionMode === 'enhancement' ? 180 : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {selectionMode === 'enhancement' ? (
              <X className="h-3.5 w-3.5 sm:h-4 sm:w-4" aria-hidden="true" />
            ) : (
              <Sparkles className="h-3.5 w-3.5 sm:h-4 sm:w-4" aria-hidden="true" />
            )}
          </motion.div>
          
          <span className="hidden xs:inline">
            {selectionMode === 'enhancement' ? 'Cancel' : 'Enhance'}
          </span>
          <span className="xs:hidden">
            {selectionMode === 'enhancement' ? 'Cancel Selection' : 'Enhance Chapters'}
          </span>
          
          {/* Count Badge */}
            <AnimatePresence>
              {((selectionMode === 'enhancement' && selectedCount > 0) || (selectionMode !== 'enhancement' && enhancableCount > 0)) && (
                <motion.span
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0, opacity: 0 }}
                  className="absolute -top-2 -right-2 bg-purple-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center z-10 shadow-lg"
                >
                  {selectionMode === 'enhancement' ? selectedCount : enhancableCount}
                </motion.span>
              )}
            </AnimatePresence>
        </motion.button>
      </motion.div>
    </div>
  );
};

/**
 * Enhanced Search Input with better UX
 */
interface EnhancedSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onClear?: () => void;
}

export const EnhancedSearchInput: React.FC<EnhancedSearchInputProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  className,
  onClear
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClear = () => {
    onChange('');
    onClear?.();
    inputRef.current?.focus();
  };

  return (
    <motion.div 
      className={cn('relative', className)}
      animate={{
        scale: isFocused ? 1.02 : 1
      }}
      transition={{ duration: 0.2 }}
    >
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
        />
        <AnimatePresence>
          {value && (
            <motion.button
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              onClick={handleClear}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </motion.button>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

/**
 * Enhanced Stats Display
 */
interface StatsDisplayProps {
  stats: {
    total: number;
    scraped: number;
    enhanced: number;
    selected: number;
  };
  className?: string;
}

export const EnhancedStatsDisplay: React.FC<StatsDisplayProps> = ({
  stats,
  className
}) => {
  return (
    <motion.div 
      className={cn('flex flex-wrap gap-2', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, staggerChildren: 0.1 }}
    >
      <motion.div 
        className="flex items-center gap-1 text-xs text-gray-400"
        whileHover={{ scale: 1.05 }}
      >
        <span className="font-medium">Total:</span>
        <Badge variant="outline" className="text-xs">
          {stats.total}
        </Badge>
      </motion.div>
      
      <motion.div 
        className="flex items-center gap-1 text-xs text-gray-400"
        whileHover={{ scale: 1.05 }}
      >
        <span className="font-medium">Scraped:</span>
        <Badge variant="secondary" className="bg-green-500/20 text-green-400 text-xs">
          {stats.scraped}
        </Badge>
      </motion.div>
      
      <motion.div 
        className="flex items-center gap-1 text-xs text-gray-400"
        whileHover={{ scale: 1.05 }}
      >
        <span className="font-medium">Enhanced:</span>
        <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 text-xs">
          {stats.enhanced}
        </Badge>
      </motion.div>
      
      {stats.selected > 0 && (
        <motion.div 
          className="flex items-center gap-1 text-xs text-gray-400"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          whileHover={{ scale: 1.05 }}
        >
          <span className="font-medium">Selected:</span>
          <Badge variant="default" className="bg-purple-500/20 text-purple-400 text-xs">
            {stats.selected}
          </Badge>
        </motion.div>
      )}
    </motion.div>
  );
};

export default {
  EnhancedHamburger,
  EnhancedFilterControls,
  EnhancedActionButtons,
  EnhancedSearchInput,
  EnhancedStatsDisplay
};