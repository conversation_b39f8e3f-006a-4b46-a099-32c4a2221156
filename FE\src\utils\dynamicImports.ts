'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { ComponentType, Suspense } from 'react';

// ============================================================================
// Dynamic Import Utilities for Performance Optimization
// ============================================================================

/**
 * Loading component for dynamic imports
 */
const LoadingSpinner = () => {
  return React.createElement('div', 
    { className: 'flex items-center justify-center p-8' },
    React.createElement('div', { className: 'animate-spin rounded-full h-8 w-8 border-b-2 border-primary' })
  );
};

const LoadingCard = () => {
  return React.createElement('div',
    { className: 'animate-pulse' },
    React.createElement('div', { className: 'bg-gray-200 dark:bg-gray-700 rounded-lg h-48 w-full' })
  );
};

const LoadingModal = () => {
  return React.createElement('div',
    { className: 'fixed inset-0 bg-black/50 flex items-center justify-center z-50' },
    React.createElement('div',
      { className: 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4' },
      React.createElement('div',
        { className: 'animate-pulse space-y-4' },
        React.createElement('div', { className: 'h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4' }),
        React.createElement('div', { className: 'h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2' }),
        React.createElement('div', { className: 'h-32 bg-gray-200 dark:bg-gray-700 rounded' })
      )
    )
  );
};

// ============================================================================
// Dynamically Imported Components
// ============================================================================

/**
 * Heavy components that should be lazy loaded
 */
export const DynamicChapterListModal = dynamic(
  () => import('@/components/reader/ChapterListModal'),
  {
    loading: LoadingModal,
    ssr: false
  }
);

export const DynamicScrapingProgress = dynamic(
  () => import('@/components/scrape/ScrapingProgress'),
  {
    loading: LoadingCard,
    ssr: false
  }
);

export const DynamicBatchActionsPanel = dynamic(
  () => import('@/components/story/StoryChapterList/BatchActionsPanel').then(mod => ({ default: mod.BatchActionsPanel })),
  {
    loading: LoadingSpinner,
    ssr: false
  }
);

export const DynamicErrorBoundary = dynamic(
  () => import('@/components/ErrorBoundary'),
  {
    loading: () => React.createElement('div', null, 'Loading error handler...'),
    ssr: true // Error boundary should be SSR
  }
);

/**
 * Chart and visualization components (heavy dependencies)
 */
export const DynamicChart = dynamic(
  () => import('recharts').then(mod => ({ default: mod.LineChart })),
  {
    loading: LoadingSpinner,
    ssr: false
  }
);

/**
 * Code editor components (very heavy)
 */
export const DynamicCodeEditor = dynamic(
  () => import('@monaco-editor/react').then(mod => ({ default: mod.default })),
  {
    loading: () => React.createElement('div', 
      { className: 'border rounded-lg p-4 bg-gray-50 dark:bg-gray-900' },
      React.createElement('div', 
        { className: 'animate-pulse space-y-2' },
        React.createElement('div', { className: 'h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4' }),
        React.createElement('div', { className: 'h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4' }),
        React.createElement('div', { className: 'h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2' })
      )
    ),
    ssr: false
  }
);

// ============================================================================
// Dynamic Import Helpers
// ============================================================================

/**
 * Create a dynamic component with custom loading state
 */
export const createDynamicComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T } | T>,
  options: {
    loading?: () => React.ReactElement;
    ssr?: boolean;
    suspense?: boolean;
  } = {}
) => {
  const { loading = LoadingSpinner, ssr = false, suspense = false } = options;

  const DynamicComponent = dynamic(importFn, {
    loading,
    ssr,
    suspense
  });

  return DynamicComponent;
};

/**
 * Preload a dynamic component
 */
export const preloadComponent = async (importFn: () => Promise<any>) => {
  try {
    await importFn();
  } catch (error) {
    // Failed to preload component
  }
};

/**
 * Lazy load utility with retry logic
 */
export const lazyWithRetry = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  maxRetries: number = 3
) => {
  return dynamic(
    async () => {
      let lastError: Error | null = null;
      
      for (let i = 0; i < maxRetries; i++) {
        try {
          return await importFn();
        } catch (error) {
          lastError = error as Error;
          // Import attempt failed
          
          if (i < maxRetries - 1) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
          }
        }
      }
      
      throw lastError || new Error('Failed to import component after retries');
    },
    {
      loading: LoadingSpinner,
      ssr: false
    }
  );
};

/**
 * Bundle splitting utility for feature-based code splitting
 */
export const createFeatureBundle = (featureName: string) => {
  return {
    // Main feature component
    Component: dynamic(
      () => import(`@/features/${featureName}/index`),
      {
        loading: LoadingSpinner,
        ssr: false
      }
    ),
    
    // Feature-specific hooks
    hooks: {
      useFeature: () => import(`@/features/${featureName}/hooks`),
    },
    
    // Feature-specific services
    services: {
      api: () => import(`@/features/${featureName}/services`),
    },
    
    // Feature-specific types
    types: () => import(`@/features/${featureName}/types`),
  };
};

/**
 * Conditional dynamic import based on feature flags
 */
export const conditionalImport = <T extends ComponentType<any>>(
  condition: boolean | (() => boolean),
  importFn: () => Promise<{ default: T }>,
  fallback?: ComponentType
) => {
  const shouldImport = typeof condition === 'function' ? condition() : condition;
  
  if (!shouldImport && fallback) {
    return fallback;
  }
  
  return shouldImport ? dynamic(importFn, {
    loading: LoadingSpinner,
    ssr: false
  }) : null;
};

/**
 * Performance monitoring for dynamic imports
 */
export const monitoredDynamicImport = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  componentName: string
) => {
  return dynamic(
    async () => {
      const startTime = performance.now();
      
      try {
        const importedModule = await importFn();
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        if (process.env.NODE_ENV === 'development') {
          // Dynamic import timing removed
        }
        
        return importedModule;
      } catch (error) {
        // Failed to load component
        throw error;
      }
    },
    {
      loading: LoadingSpinner,
      ssr: false
    }
  );
};

// ============================================================================
// Preloading Strategies
// ============================================================================

/**
 * Preload components on user interaction
 */
export const preloadOnHover = (importFn: () => Promise<any>) => {
  let preloaded = false;
  
  return {
    onMouseEnter: () => {
      if (!preloaded) {
        preloaded = true;
        preloadComponent(importFn);
      }
    }
  };
};

/**
 * Preload components on viewport intersection
 */
export const preloadOnIntersection = (
  importFn: () => Promise<any>,
  options: IntersectionObserverInit = {}
) => {
  let preloaded = false;
  
  return {
    ref: (element: HTMLElement | null) => {
      if (!element || preloaded) return;
      
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && !preloaded) {
            preloaded = true;
            preloadComponent(importFn);
            observer.disconnect();
          }
        },
        { threshold: 0.1, ...options }
      );
      
      observer.observe(element);
    }
  };
};

/**
 * Preload components after initial page load
 */
export const preloadAfterMount = (importFns: Array<() => Promise<any>>, delay: number = 2000) => {
  if (typeof window !== 'undefined') {
    setTimeout(() => {
      importFns.forEach(importFn => {
        preloadComponent(importFn);
      });
    }, delay);
  }
};