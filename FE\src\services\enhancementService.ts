/**
 * Chapter Enhancement Service
 * 
 * Provides functionality for enhancing chapter content using AI/ML services.
 * This service handles batch enhancement operations with progress tracking.
 * Updated to work with the new task-based API structure.
 */

import { BaseService, PaginationParams, API_ENDPOINTS } from './BaseService';

// Job History Response interface
export interface JobHistoryResponse {
  data: JobStatusResponse[];
  pagination: {
    page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
}
import { jobManagementService, JobStatusResponse } from './jobManagementService';

// Types for enhancement operations
export interface ChapterEnhancementRequest {
  chapter_id: string;
  force_re_enhance?: boolean;
}

export interface BatchEnhancementRequest {
  chapter_ids?: string[];
  story_id?: string;
  force_re_enhance?: boolean;
  batch_size?: number;
}

export interface EnhancementResult {
  chapter_id: string;
  original_length: number;
  enhanced_length: number;
  improvement_notes: string[];
  enhancement_duration?: number;
  quality_score?: number;
}

export interface BatchEnhancementResponse {
  success: boolean;
  message: string;
  timestamp: string;
  job_id: string;
  total_chapters: number;
  completed_chapters: number;
  results: EnhancementResult[];
  failed_chapters: string[];
}



// Legacy interface for backward compatibility
export interface EnhancementProgress extends JobStatusResponse {
  total_chapters: number;
  failed_chapters: number;
  current_chapter?: string;
  results: EnhancementResult[];
}

// Enhancement Service using BaseService
class EnhancementApiService extends BaseService {
  constructor() {
    super();
  }

  async enhanceSingleChapter(request: ChapterEnhancementRequest): Promise<{ message: string }> {
    return this.post<{ message: string }>(API_ENDPOINTS.ENHANCEMENT.SINGLE, request);
  }

  async startBatchEnhancement(request: BatchEnhancementRequest): Promise<BatchEnhancementResponse> {
    console.log('startBatchEnhancement', request);
    return this.post<BatchEnhancementResponse>(API_ENDPOINTS.ENHANCEMENT.BATCH, request);
  }

  async testEnhancement(): Promise<{ message: string }> {
    return this.get<{ message: string }>(API_ENDPOINTS.ENHANCEMENT.TEST);
  }
}



// Create service instances
const enhancementApiService = new EnhancementApiService();


class EnhancementService {
  private activeJobs = new Map<string, EnhancementProgress>();
  private progressCallbacks = new Map<string, (progress: EnhancementProgress) => void>();
  private pollingIntervals = new Map<string, NodeJS.Timeout>();

  /**
   * Enhance a single chapter
   */
  async enhanceSingleChapter(
    chapterId: string,
    forceReEnhance: boolean = false
  ): Promise<{ success: boolean; message: string; timestamp: string }> {
    const request: ChapterEnhancementRequest = {
      chapter_id: chapterId,
      force_re_enhance: forceReEnhance
    };

    try {
      const response = await enhancementApiService.enhanceSingleChapter(request);
      return {
        success: true,
        message: response.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to enhance single chapter:', error);
      throw error;
    }
  }

  /**
   * Start batch enhancement for specific chapters
   */
  async startBatchEnhancement(
    request: BatchEnhancementRequest,
    options: {
      onProgress?: (progress: EnhancementProgress) => void;
    } = {}
  ): Promise<BatchEnhancementResponse> {
    try {
      const response = await enhancementApiService.startBatchEnhancement(request);

      // Start progress tracking if callback provided
      if (options.onProgress) {
        this.startProgressPolling(response.job_id, options.onProgress);
      }

      return response;
    } catch (error) {
      console.error('Failed to start batch enhancement:', error);
      throw error;
    }
  }

  /**
   * Get job status and progress for a specific job
   */
  async getJobStatus(jobId: string, jobType: string = 'enhancement'): Promise<JobStatusResponse> {
    try {
      const response = await jobManagementService.getJobStatus(jobId, jobType);
      return response;
    } catch (error) {
      console.error('Failed to get job status:', error);
      throw error;
    }
  }

  /**
   * Get enhancement progress for a specific job (legacy method for backward compatibility)
   */
  async getEnhancementProgress(jobId: string): Promise<EnhancementProgress> {
    try {
      const status = await this.getJobStatus(jobId, 'enhancement');
      
      // Convert JobStatusResponse to EnhancementProgress for backward compatibility
      const progress: EnhancementProgress = {
        ...status,
        total_chapters: status.total_items || 0,
        failed_chapters: status.failed_items,
        results: [] // This would need to be populated from enhancement results if needed
      };
      
      // Update local cache
      this.activeJobs.set(jobId, progress);
      
      return progress;
    } catch (error) {
      console.error('Failed to get enhancement progress:', error);
      throw error;
    }
  }

  /**
   * Start polling for progress updates
   */
  startProgressPolling(
    jobId: string,
    onProgress: (progress: EnhancementProgress) => void,
    intervalMs: number = 3000 // Longer interval for AI operations
  ): void {
    // Store callback
    this.progressCallbacks.set(jobId, onProgress);

    // Clear existing interval if any
    const existingInterval = this.pollingIntervals.get(jobId);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // Start polling
    const interval = setInterval(async () => {
      try {
        const progress = await this.getEnhancementProgress(jobId);
        onProgress(progress);

        // Stop polling if job is complete
        if (['completed', 'failed', 'cancelled'].includes(progress.status)) {
          this.stopProgressPolling(jobId);
        }
      } catch (error) {
        console.error('Error polling enhancement progress:', error);
        // Continue polling on error, but notify callback
        onProgress({
          success: false,
          message: 'Polling failed',
          timestamp: new Date().toISOString(),
          job_id: jobId,
          job_type: 'enhancement',
          status: 'failed',
          progress_percentage: 0,
          total_items: 0,
          completed_items: 0,
          failed_items: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error'],
          created_at: new Date().toISOString(),
          total_chapters: 0,
          failed_chapters: 0,
          results: []
        });
      }
    }, intervalMs);

    this.pollingIntervals.set(jobId, interval);
  }

  /**
   * Stop polling for a specific job
   */
  stopProgressPolling(jobId: string): void {
    const interval = this.pollingIntervals.get(jobId);
    if (interval) {
      clearInterval(interval);
      this.pollingIntervals.delete(jobId);
    }
    this.progressCallbacks.delete(jobId);
  }

  /**
   * Cancel a running job
   */
  async cancelJob(jobId: string, jobType: string = 'enhancement'): Promise<{ success: boolean; message: string; timestamp: string }> {
    try {
      const response = await jobManagementService.cancelJob(jobId, jobType);
      
      this.stopProgressPolling(jobId);
      this.activeJobs.delete(jobId);
      
      return {
        success: true,
        message: response.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to cancel job:', error);
      throw error;
    }
  }

  /**
   * Cancel a running enhancement job (legacy method for backward compatibility)
   */
  async cancelEnhancementJob(jobId: string): Promise<void> {
    try {
      await this.cancelJob(jobId, 'enhancement');
    } catch (error) {
      console.error('Failed to cancel enhancement job:', error);
      throw error;
    }
  }

  /**
   * Get cached progress for a job
   */
  getCachedProgress(jobId: string): EnhancementProgress | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Test enhancement service connectivity
   */
  async testEnhancementService(): Promise<{ success: boolean; message: string; timestamp: string }> {
    try {
      const response = await enhancementApiService.testEnhancement();
      return {
        success: true,
        message: response.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to test enhancement service:', error);
      throw error;
    }
  }

  /**
   * Cleanup all active polling
   */
  cleanup(): void {
    this.pollingIntervals.forEach((interval) => clearInterval(interval));
    this.pollingIntervals.clear();
    this.progressCallbacks.clear();
    this.activeJobs.clear();
  }

  /**
   * Get list of active jobs
   */
  async getActiveJobs(jobType?: string): Promise<JobStatusResponse[]> {
    try {
      const response = await jobManagementService.getActiveJobs(jobType);
      return response;
    } catch (error) {
      console.error('Failed to get active jobs:', error);
      throw error;
    }
  }

  /**
   * Get job history with pagination
   */
  async getJobHistory(params?: PaginationParams & { job_type?: string; status?: string }): Promise<JobHistoryResponse> {
    try {
      const response = await jobManagementService.getJobHistory(params);
      return response;
    } catch (error) {
      console.error('Failed to get job history:', error);
      throw error;
    }
  }

  /**
   * Get detailed job information
   */
  async getJobDetails(jobId: string): Promise<JobStatusResponse> {
    try {
      const response = await jobManagementService.getJobDetails(jobId);
      return response;
    } catch (error) {
      console.error('Failed to get job details:', error);
      throw error;
    }
  }

  /**
   * Retry a failed job
   */
  async retryJob(jobId: string): Promise<{ success: boolean; message: string; new_job_id: string; timestamp: string }> {
    try {
      const response = await jobManagementService.retryJob(jobId);
      return {
        success: true,
        message: response.message,
        new_job_id: response.new_job_id,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to retry job:', error);
      throw error;
    }
  }

  /**
   * Delete a job
   */
  async deleteJob(jobId: string): Promise<{ success: boolean; message: string; timestamp: string }> {
    try {
      const response = await jobManagementService.deleteJob(jobId);
      return {
        success: true,
        message: response.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to delete job:', error);
      throw error;
    }
  }

  /**
   * Bulk cancel multiple jobs
   */
  async bulkCancelJobs(jobIds: string[], jobType?: string): Promise<{ success: boolean; cancelled_count: number; failed_count: number; timestamp: string }> {
    try {
      const response = await jobManagementService.bulkCancelJobs({ job_ids: jobIds, job_type: jobType });
      return {
        success: true,
        cancelled_count: response.cancelled_count,
        failed_count: response.failed_count,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to bulk cancel jobs:', error);
      throw error;
    }
  }

  /**
   * Get job logs
   */
  async getJobLogs(jobId: string, params?: { level?: string; limit?: number }): Promise<{ success: boolean; logs: any[]; timestamp: string }> {
    try {
      const response = await jobManagementService.getJobLogs(jobId, params);
      return {
        success: true,
        logs: response.logs,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get job logs:', error);
      throw error;
    }
  }

  /**
   * Batch enhance with comprehensive progress tracking
   */
  async batchEnhanceWithProgress(
    request: BatchEnhancementRequest,
    options: {
      onProgress?: (progress: EnhancementProgress) => void;
      onComplete?: (results: BatchEnhancementResponse) => void;
      onError?: (error: Error) => void;
    } = {}
  ): Promise<string | null> {
    try {
      // Start batch enhancement
      const response = await this.startBatchEnhancement(request, {
        onProgress: options.onProgress
      });

      const { onProgress: originalOnProgress } = options;
      
      // Enhanced progress callback that handles completion
      const enhancedOnProgress = (progress: EnhancementProgress) => {
        if (originalOnProgress) {
          originalOnProgress(progress);
        }
        
        // Check for completion
        if (progress.status === 'completed' && options.onComplete) {
          const completedResponse: BatchEnhancementResponse = {
            success: true,
            message: 'Batch enhancement completed',
            timestamp: new Date().toISOString(),
            job_id: progress.job_id,
            total_chapters: progress.total_chapters,
            completed_chapters: progress.completed_items,
            results: progress.results,
            failed_chapters: []
          };
          options.onComplete(completedResponse);
        }
        
        // Check for errors
        if (progress.status === 'failed' && options.onError) {
          options.onError(new Error('Enhancement job failed'));
        }
      };
      
      // Start progress polling with enhanced callback
      this.startProgressPolling(response.job_id, enhancedOnProgress);
      
      return response.job_id;
    } catch (error) {
      if (options.onError) {
        options.onError(error as Error);
      }
      return null;
    }
  }
}

// Export singleton instance
export const enhancementService = new EnhancementService();

// Export service instances for direct use
export { enhancementApiService };

// Utility functions
export const formatEnhancementProgress = (progress: EnhancementProgress | JobStatusResponse): string => {
  const total = 'total_chapters' in progress ? progress.total_chapters : (progress.total_items || 0);
  const completed = 'completed_chapters' in progress ? progress.completed_chapters : progress.completed_items;
  return `${completed}/${total} (${Math.round(progress.progress_percentage)}%)`;
};

export const getEnhancementStatusColor = (status: JobStatusResponse['status']): string => {
  switch (status) {
    case 'pending': return 'text-yellow-500';
    case 'in_progress': return 'text-blue-500';
    case 'completed': return 'text-green-500';
    case 'failed': return 'text-red-500';
    case 'cancelled': return 'text-gray-500';
    default: return 'text-gray-400';
  }
};

// Helper function to create batch enhancement request
export const createBatchEnhancementRequest = (
  chapterIds?: string[],
  storyId?: string,
  options: {
    forceReEnhance?: boolean;
    batchSize?: number;
  } = {}
): BatchEnhancementRequest => {
  return {
    chapter_ids: chapterIds,
    story_id: storyId,
    force_re_enhance: options.forceReEnhance || true,
    batch_size: options.batchSize || 1
  };
};

// Helper function to convert JobStatusResponse to EnhancementProgress for legacy compatibility
export const convertJobStatusToEnhancementProgress = (
  jobStatus: JobStatusResponse,
  results: EnhancementResult[] = []
): EnhancementProgress => {
  return {
    ...jobStatus,
    total_chapters: jobStatus.total_items || 0,
    failed_chapters: jobStatus.failed_items,
    results
  };
};