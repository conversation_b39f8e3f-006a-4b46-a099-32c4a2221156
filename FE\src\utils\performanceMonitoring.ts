'use client';

import React from 'react';

// ============================================================================
// Performance Monitoring Utilities
// ============================================================================

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  renderCount: number;
  timestamp: number;
  props?: Record<string, any>;
  memoryUsage?: number;
}

/**
 * Performance monitoring class
 */
class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private observers: Map<string, PerformanceObserver> = new Map();
  private isEnabled: boolean = process.env.NODE_ENV === 'development';

  /**
   * Start monitoring a component
   */
  startMonitoring(componentName: string, props?: Record<string, any>) {
    if (!this.isEnabled) return;

    const startTime = performance.now();
    return {
      end: () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        this.recordMetric({
          componentName,
          renderTime,
          renderCount: this.getRenderCount(componentName) + 1,
          timestamp: Date.now(),
          props,
          memoryUsage: this.getMemoryUsage()
        });
      }
    };
  }

  /**
   * Record a performance metric
   */
  private recordMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Log slow renders
    if (metric.renderTime > 16) { // 16ms = 60fps threshold
      // Slow render detection removed
    }
  }

  /**
   * Get render count for a component
   */
  private getRenderCount(componentName: string): number {
    return this.metrics.filter(m => m.componentName === componentName).length;
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Get performance report for a component
   */
  getComponentReport(componentName: string) {
    const componentMetrics = this.metrics.filter(m => m.componentName === componentName);
    
    if (componentMetrics.length === 0) {
      return null;
    }

    const renderTimes = componentMetrics.map(m => m.renderTime);
    const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
    const maxRenderTime = Math.max(...renderTimes);
    const minRenderTime = Math.min(...renderTimes);

    return {
      componentName,
      totalRenders: componentMetrics.length,
      avgRenderTime: Number(avgRenderTime.toFixed(2)),
      maxRenderTime: Number(maxRenderTime.toFixed(2)),
      minRenderTime: Number(minRenderTime.toFixed(2)),
      lastRender: componentMetrics[componentMetrics.length - 1]?.timestamp
    };
  }

  /**
   * Get overall performance report
   */
  getOverallReport() {
    const componentNames = Array.from(new Set(this.metrics.map(m => m.componentName)));
    
    return {
      totalComponents: componentNames.length,
      totalRenders: this.metrics.length,
      components: componentNames.map(name => this.getComponentReport(name)).filter(Boolean),
      slowestComponents: this.metrics
        .sort((a, b) => b.renderTime - a.renderTime)
        .slice(0, 5)
        .map(m => ({
          componentName: m.componentName,
          renderTime: m.renderTime,
          timestamp: m.timestamp
        }))
    };
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = [];
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for performance monitoring
 */
export const usePerformanceMonitor = (componentName: string, props?: Record<string, any>) => {
  const renderStartTime = React.useRef<number>(0);
  const renderCount = React.useRef<number>(0);

  // Start timing on render
  renderStartTime.current = performance.now();
  renderCount.current += 1;

  React.useEffect(() => {
    // End timing after render
    const endTime = performance.now();
    const renderTime = endTime - renderStartTime.current;
    
    performanceMonitor.startMonitoring(componentName, props)?.end();
    
    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      // Performance logging removed
    }
  });

  return {
    renderCount: renderCount.current,
    getReport: () => performanceMonitor.getComponentReport(componentName)
  };
};

/**
 * Bundle size monitoring
 */
export const bundleAnalyzer = {
  /**
   * Log bundle information
   */
  logBundleInfo() {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      const totalSize = scripts.reduce((total, script) => {
        const src = script.getAttribute('src');
        if (src && src.includes('/_next/')) {
          // Estimate size based on script presence
          return total + 1;
        }
        return total;
      }, 0);
      
      // Bundle size logging removed
    }
  },

  /**
   * Monitor chunk loading
   */
  monitorChunkLoading() {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const originalFetch = window.fetch;
      
      window.fetch = async (...args) => {
        const [url] = args;
        const startTime = performance.now();
        
        try {
          const response = await originalFetch(...args);
          const endTime = performance.now();
          const loadTime = endTime - startTime;
          
          if (typeof url === 'string' && url.includes('/_next/')) {
            // Chunk loading logging removed
          }
          
          return response;
        } catch (error) {
          // Chunk loading error removed
          throw error;
        }
      };
    }
  }
};

/**
 * Memory usage monitoring
 */
export const memoryMonitor = {
  /**
   * Get current memory usage
   */
  getCurrentUsage() {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
      };
    }
    return null;
  },

  /**
   * Start memory monitoring
   */
  startMonitoring(interval: number = 5000) {
    if (process.env.NODE_ENV !== 'development') return;
    
    const monitor = setInterval(() => {
      const usage = this.getCurrentUsage();
      if (usage) {
        // Memory usage logging removed
        
        // Warn if memory usage is high
        if (usage.used / usage.limit > 0.8) {
          // High memory usage warning removed
        }
      }
    }, interval);
    
    return () => clearInterval(monitor);
  }
};

/**
 * Performance debugging utilities
 */
export const performanceDebugger = {
  /**
   * Log all performance metrics
   */
  logAllMetrics() {
    if (process.env.NODE_ENV === 'development') {
      // Performance metrics logging removed
    }
  },

  /**
   * Log component-specific metrics
   */
  logComponentMetrics(componentName: string) {
    if (process.env.NODE_ENV === 'development') {
      const report = performanceMonitor.getComponentReport(componentName);
      if (report) {
        // Component metrics logging removed
      }
    }
  },

  /**
   * Start comprehensive monitoring
   */
  startComprehensiveMonitoring() {
    if (process.env.NODE_ENV === 'development') {
      bundleAnalyzer.monitorChunkLoading();
      const stopMemoryMonitoring = memoryMonitor.startMonitoring();
      
      // Log reports every 30 seconds
      const reportInterval = setInterval(() => {
        this.logAllMetrics();
      }, 30000);
      
      return () => {
        stopMemoryMonitoring?.();
        clearInterval(reportInterval);
      };
    }
  }
};

// Auto-start monitoring in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  bundleAnalyzer.logBundleInfo();
}