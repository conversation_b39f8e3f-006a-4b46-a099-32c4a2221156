'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import OptimizedImage from '@/components/ui/OptimizedImage';

// Base card context
interface CardContextValue {
  isHoverable: boolean;
  isClickable: boolean;
}

const CardContext = React.createContext<CardContextValue>({
  isHoverable: false,
  isClickable: false
});

// Root card component
interface CompoundCardProps {
  children: React.ReactNode;
  className?: string;
  hoverable?: boolean;
  clickable?: boolean;
  href?: string;
}

const CompoundCardRoot: React.FC<CompoundCardProps> = ({
  children,
  className,
  hoverable = false,
  clickable = false,
  href
}) => {
  const contextValue = {
    isHoverable: hoverable,
    isClickable: clickable
  };

  const cardClasses = cn(
    'overflow-hidden border-border/50 bg-card/50',
    {
      'hover:shadow-xl transition-all duration-300 hover:scale-105': hoverable,
      'cursor-pointer': clickable || href,
      'group': hoverable || clickable || href
    },
    className
  );

  // Removed unused variables

  return (
    <CardContext.Provider value={contextValue}>
      {href ? (
        <Link href={href} className="group">
          <Card className={cardClasses}>
            {children}
          </Card>
        </Link>
      ) : (
        <Card className={cardClasses}>
          {children}
        </Card>
      )}
    </CardContext.Provider>
  );
};

// Image component with aspect ratio support
interface CompoundCardImageProps {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  overlay?: React.ReactNode;
}

const CompoundCardImage: React.FC<CompoundCardImageProps> = ({
  src,
  alt,
  aspectRatio = 'portrait',
  className,
  priority = false,
  quality = 80,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  overlay
}) => {
  const { isHoverable } = React.useContext(CardContext);

  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]'
  };

  return (
    <div className={cn('relative bg-muted', aspectClasses[aspectRatio], className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        className={cn(
          'object-cover',
          {
            'group-hover:scale-110 transition-transform duration-300': isHoverable
          }
        )}
        sizes={sizes}
        priority={priority}
        quality={quality}
      />
      {overlay && (
        <div className="absolute inset-0 flex items-start justify-end p-2">
          {overlay}
        </div>
      )}
    </div>
  );
};

// Header component
interface CompoundCardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

const CompoundCardHeader: React.FC<CompoundCardHeaderProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('p-4 pb-2', className)}>
      {children}
    </div>
  );
};

// Title component
interface CompoundCardTitleProps {
  children: React.ReactNode;
  className?: string;
  truncate?: boolean;
  lines?: number;
}

const CompoundCardTitle: React.FC<CompoundCardTitleProps> = ({
  children,
  className,
  truncate = true,
  lines = 1
}) => {
  const { isHoverable } = React.useContext(CardContext);

  const truncateClasses = {
    1: 'truncate',
    2: 'line-clamp-2',
    3: 'line-clamp-3'
  };

  return (
    <h3 className={cn(
      'font-semibold text-foreground text-lg mb-2 overflow-hidden',
      {
        'group-hover:text-primary transition-colors': isHoverable,
        [truncateClasses[lines as keyof typeof truncateClasses] || 'line-clamp-2']: truncate
      },
      className
    )}>
      {truncate ? (
        <span className="block">{children}</span>
      ) : (
        children
      )}
    </h3>
  );
};

// Description component
interface CompoundCardDescriptionProps {
  children: React.ReactNode;
  className?: string;
  lines?: number;
}

const CompoundCardDescription: React.FC<CompoundCardDescriptionProps> = ({
  children,
  className,
  lines = 2
}) => {
  const lineClampClasses = {
    1: 'line-clamp-1',
    2: 'line-clamp-2',
    3: 'line-clamp-3'
  };

  return (
    <p className={cn(
      'text-muted-foreground text-sm mb-3 overflow-hidden',
      lineClampClasses[lines as keyof typeof lineClampClasses] || 'line-clamp-2',
      className
    )}>
      {children}
    </p>
  );
};

// Content component
interface CompoundCardContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

const CompoundCardContent: React.FC<CompoundCardContentProps> = ({
  children,
  className,
  padding = 'md'
}) => {
  const paddingClasses = {
    none: 'p-0',
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6'
  };

  return (
    <CardContent className={cn(paddingClasses[padding], className)}>
      {children}
    </CardContent>
  );
};

// Footer component
interface CompoundCardFooterProps {
  children: React.ReactNode;
  className?: string;
  justify?: 'start' | 'center' | 'end' | 'between';
}

const CompoundCardFooter: React.FC<CompoundCardFooterProps> = ({
  children,
  className,
  justify = 'between'
}) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div className={cn(
      'flex items-center text-xs text-muted-foreground',
      justifyClasses[justify],
      className
    )}>
      {children}
    </div>
  );
};

// Meta information component
interface CompoundCardMetaProps {
  label: string;
  value: string | number;
  className?: string;
}

const CompoundCardMeta: React.FC<CompoundCardMetaProps> = ({
  label,
  value,
  className
}) => {
  return (
    <div className={cn('text-muted-foreground text-sm', className)}>
      <span className="font-medium">{label}:</span> {value}
    </div>
  );
};

// Export compound component
export const CompoundCard = {
  Root: CompoundCardRoot,
  Image: CompoundCardImage,
  Header: CompoundCardHeader,
  Title: CompoundCardTitle,
  Description: CompoundCardDescription,
  Content: CompoundCardContent,
  Footer: CompoundCardFooter,
  Meta: CompoundCardMeta
};

export default CompoundCard;