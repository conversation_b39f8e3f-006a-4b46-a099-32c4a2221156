'use client';

import React, { memo, useMemo, useCallback, forwardRef } from 'react';
import { cn } from '@/lib/utils';

// ============================================================================
// Memoized Components for Performance
// ============================================================================

/**
 * Optimized Card component with memoization
 */
interface OptimizedCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  isSelected?: boolean;
  isLoading?: boolean;
}

export const OptimizedCard = memo<OptimizedCardProps>(({ 
  children, 
  className, 
  onClick, 
  isSelected = false,
  isLoading = false 
}) => {
  const cardClasses = useMemo(() => cn(
    'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200',
    {
      'ring-2 ring-primary': isSelected,
      'opacity-50 pointer-events-none': isLoading,
      'hover:shadow-md cursor-pointer': onClick,
    },
    className
  ), [className, isSelected, isLoading, onClick]);

  const handleClick = useCallback(() => {
    if (!isLoading && onClick) {
      onClick();
    }
  }, [isLoading, onClick]);

  return (
    <div className={cardClasses} onClick={handleClick}>
      {children}
    </div>
  );
});

OptimizedCard.displayName = 'OptimizedCard';

/**
 * Optimized List component with virtualization support
 */
interface OptimizedListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  keyExtractor: (item: T, index: number) => string;
  className?: string;
  emptyMessage?: string;
  isLoading?: boolean;
  loadingComponent?: React.ReactNode;
}

function OptimizedListInner<T>({
  items,
  renderItem,
  keyExtractor,
  className,
  emptyMessage = 'No items found',
  isLoading = false,
  loadingComponent
}: OptimizedListProps<T>) {
  const memoizedItems = useMemo(() => 
    items.map((item, index) => ({
      key: keyExtractor(item, index),
      element: renderItem(item, index)
    })),
    [items, renderItem, keyExtractor]
  );

  if (isLoading) {
    return loadingComponent || (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="text-center text-muted-foreground p-8">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {memoizedItems.map(({ key, element }) => (
        <div key={key}>{element}</div>
      ))}
    </div>
  );
}

export const OptimizedList = memo(OptimizedListInner) as typeof OptimizedListInner & {
  displayName?: string;
};

OptimizedList.displayName = 'OptimizedList';

/**
 * Optimized Image component with lazy loading
 */
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage = memo<OptimizedImageProps>(({ 
  src, 
  alt, 
  width, 
  height, 
  className, 
  priority = false,
  onLoad,
  onError 
}) => {
  const handleLoad = useCallback(() => {
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    onError?.();
  }, [onError]);

  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={cn('object-cover', className)}
      loading={priority ? 'eager' : 'lazy'}
      onLoad={handleLoad}
      onError={handleError}
    />
  );
});

OptimizedImage.displayName = 'OptimizedImage';

/**
 * Optimized Button component with debouncing
 */
interface OptimizedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  debounceMs?: number;
}

export const OptimizedButton = memo<OptimizedButtonProps>(({ 
  children, 
  onClick, 
  disabled = false, 
  loading = false,
  variant = 'default',
  size = 'default',
  className,
  debounceMs = 300
}) => {
  const [isDebouncing, setIsDebouncing] = React.useState(false);

  const handleClick = useCallback(() => {
    if (disabled || loading || isDebouncing || !onClick) return;

    setIsDebouncing(true);
    onClick();

    setTimeout(() => {
      setIsDebouncing(false);
    }, debounceMs);
  }, [disabled, loading, isDebouncing, onClick, debounceMs]);

  const buttonClasses = useMemo(() => cn(
    'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
    'disabled:pointer-events-none disabled:opacity-50',
    {
      // Variants
      'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',
      'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',
      'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',
      'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
      'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',
      'text-primary underline-offset-4 hover:underline': variant === 'link',
      // Sizes
      'h-10 px-4 py-2': size === 'default',
      'h-9 rounded-md px-3': size === 'sm',
      'h-11 rounded-md px-8': size === 'lg',
      'h-10 w-10': size === 'icon',
    },
    className
  ), [variant, size, className]);

  return (
    <button
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading || isDebouncing}
    >
      {loading && (
        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
      )}
      {children}
    </button>
  );
});

OptimizedButton.displayName = 'OptimizedButton';

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = React.useRef(0);
  const startTime = React.useRef(Date.now());

  React.useEffect(() => {
    renderCount.current += 1;
    const endTime = Date.now();
    const renderTime = endTime - startTime.current;

    if (process.env.NODE_ENV === 'development') {
      // Performance monitoring removed
    }

    startTime.current = endTime;
  });

  return {
    renderCount: renderCount.current,
    logRender: useCallback((additionalInfo?: string) => {
      if (process.env.NODE_ENV === 'development') {
        // Performance logging removed
      }
    }, [componentName])
  };
};

/**
 * Debounced value hook for performance optimization
 */
export const useDebounce = (value: any, delay: number): any => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Throttled callback hook
 */
export function useThrottle(
  callback: Function,
  delay: number
) {
  const lastCall = React.useRef<number>(0);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    const now = Date.now();
    
    if (now - lastCall.current >= delay) {
      lastCall.current = now;
      callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCall.current = Date.now();
        callback(...args);
      }, delay - (now - lastCall.current));
    }
  }, [callback, delay]);
}