'use client';


import { Story } from '@/types/story';
import { useStoryStats } from '@/hooks/useStoryStats';
import OptimizedImage from '../ui/OptimizedImage';

interface StoryInfoProps {
  story: Story;
}

const StoryInfo = ({ story }: StoryInfoProps) => {
  const { 
    total_chapters, 
    loading: statsLoading 
  } = useStoryStats(story.id, story);

  return (
    <div className="bg-zinc-800/50 rounded-lg p-3 sm:p-4 lg:p-6 mb-3 sm:mb-6 lg:mb-8 border border-zinc-700/50 mx-mobile-padding sm:mx-0">
      <div className="flex flex-col md:flex-row gap-3 sm:gap-4 lg:gap-6">
        {/* Cover Image */}
        <div className="flex-shrink-0">
          <div className="relative w-32 h-44 xs:w-36 xs:h-50 sm:w-48 sm:h-64 mx-auto md:mx-0 bg-zinc-700 rounded-lg overflow-hidden shadow-lg touch-manipulation">
            {story.cover_image_url ? (
              <OptimizedImage
                src={story.cover_image_url}
                alt={`Cover image for ${story.title}`}
                fill
                className="object-cover transition-transform duration-300 hover:scale-105"
                sizes="(max-width: 475px) 128px, (max-width: 640px) 144px, (max-width: 768px) 160px, 192px"
                priority
              />
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <svg className="w-10 h-10 xs:w-12 xs:h-12 sm:w-16 sm:h-16" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
        </div>

        {/* Story Information */}
        <div className="flex-1 space-y-3 sm:space-y-4 lg:space-y-6 min-w-0">
          {/* Title and Author */}
          <div className="space-y-2">
            <h1 className="text-xl xs:text-2xl sm:text-3xl font-bold text-white leading-tight line-clamp-3 md:line-clamp-2">
              {story.title}
            </h1>
            <div className="flex flex-wrap items-center gap-2 text-sm xs:text-base sm:text-lg text-gray-300">
              <span>By {story.author}</span>
              {!statsLoading && total_chapters > 0 && (
                <>
                  <span className="text-gray-500">·</span>
                  <span className="text-indigo-400 font-medium">
                    {total_chapters} {total_chapters === 1 ? 'chapter' : 'chapters'}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 sm:gap-3 lg:gap-4">
            <div className="bg-zinc-700/50 rounded-lg p-2.5 xs:p-3 sm:p-4 touch-manipulation">
              <h3 className="text-xs xs:text-sm font-medium text-muted-foreground mb-1">Source</h3>
              <a 
                href={story.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-indigo-400 hover:text-indigo-300 active:text-indigo-500 transition-colors touch-manipulation block"
                aria-label={`Visit source: ${story.url}`}
              >
                <p className="text-xs xs:text-sm sm:text-base truncate">
                  {new URL(story.url as string).hostname}
                </p>
              </a>
            </div>

            <div className="bg-zinc-700/50 rounded-lg p-2.5 xs:p-3 sm:p-4">
              <h3 className="text-xs xs:text-sm font-medium text-muted-foreground mb-1">Updated</h3>
              <p className="text-white text-xs xs:text-sm sm:text-base">
                {new Date(story.updated_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </p>
            </div>
          </div>

          {/* Description */}
          {story.description && (
            <div className="bg-zinc-700/50 rounded-lg p-2.5 xs:p-3 sm:p-4">
              <h3 className="text-xs xs:text-sm font-medium text-muted-foreground mb-2">Description</h3>
              <div className="relative">
                <p className="text-gray-300 text-xs xs:text-sm sm:text-base leading-relaxed whitespace-pre-wrap max-h-32 xs:max-h-40 sm:max-h-48 lg:max-h-80 overflow-y-auto scrollbar-thin mobile-hide-scrollbar">
                  {story.description}
                </p>
                {story.description.length > 200 && (
                  <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-zinc-700/50 to-transparent pointer-events-none" />
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StoryInfo;