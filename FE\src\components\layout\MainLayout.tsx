'use client';

import React from 'react';

interface MainLayoutProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export const MainLayout = ({ children, header, footer }: MainLayoutProps) => {
  return (
    <div className="flex flex-col min-h-screen">
      <header role="banner">{header}</header>
      <main role="main" className="flex-grow container mx-auto p-4">
        {children}
      </main>
      <footer role="contentinfo">{footer}</footer>
    </div>
  );
};

MainLayout.displayName = 'MainLayout';