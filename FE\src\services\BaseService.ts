/**
 * Base Service Class
 * 
 * Provides common functionality for all API services including:
 * - Centralized API configuration
 * - Common request handling
 * - Error handling and retry logic
 * - Response type safety
 * - Request/response logging
 */

// ============================================================================
// Configuration
// ============================================================================

export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

export const API_PREFIX = '/api/v1';

export const API_ENDPOINTS = {
  // Scraping Operations
  SCRAPING: {
    BASE: '/api/v1/scraping',
    SCRAPE: '/api/v1/scraping/scrape',
    ASYNC: '/api/v1/scraping/scrape-async',
    BATCH: '/api/v1/scraping/batch/scrape-chapters',
    BATCH_BY_URLS: '/api/v1/scraping/batch/scrape-chapters-by-urls',
    STORY_PAGES: '/api/v1/scraping/story',
    PAGE_CHAPTERS: '/api/v1/scraping/page',
    CHAPTER_CONTENT: '/api/v1/scraping/chapter',
    CHAPTER_CONTENT_BASE: '/api/v1/scraping/chapter',
    TEST: '/api/v1/scraping/test',
  },
  
  // Stories & Chapters
  STORIES: '/api/v1/stories',
  
  // AI Enhancement
  ENHANCEMENT: {
    BASE: '/api/v1/enhancement',
    SINGLE: '/api/v1/enhancement/single',
    BATCH: '/api/v1/enhancement/batch',
    TEST: '/api/v1/enhancement/test-enhancement',
  },
  
  // Job Management
  JOBS: '/api/v1/jobs',
  
  // Data Export
  EXPORT: '/api/v1/export',
  
  // Health Checks
  HEALTH: '/health',
} as const;

// ============================================================================
// Imports
// ============================================================================

// Import standardized error types
import { createApiError, handleError, normalizeError } from '@/utils/errorHandling';
import { isApiError, ApiError, ErrorLike } from '@/types/errors';
import { getCache, setCache } from '@/lib/cache';

// ============================================================================
// Types
// ============================================================================

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page?: number;
  page_size?: number;
}

export interface PaginationResponse {
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface RequestOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  parseAsText?: boolean;
  disableCache?: boolean;
}

// ============================================================================
// Base Service Class
// ============================================================================

export class BaseService {
  protected baseUrl: string;
  protected defaultHeaders: Record<string, string>;
  private currentEndpoint?: string;
  private currentMethod?: string;
  private ongoingRequests = new Map<string, Promise<any>>();
  
  constructor(baseUrl: string = API_CONFIG.BASE_URL) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Make an API request with built-in error handling, retry logic, and deduplication
   */
  protected async makeRequest<T>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<T> {
    const {
      timeout = API_CONFIG.TIMEOUT,
      retries = API_CONFIG.RETRY_ATTEMPTS,
      retryDelay = API_CONFIG.RETRY_DELAY,
      ...fetchOptions
    } = options;

    const url = `${this.baseUrl}${endpoint}`;
    this.currentEndpoint = endpoint;
    this.currentMethod = fetchOptions.method || 'GET';

    const requestKey = `${this.currentMethod}:${url}:${JSON.stringify(
      fetchOptions.body
    )}`;

    if (this.ongoingRequests.has(requestKey)) {
      this.logRequest('DEDUPLICATED', url, fetchOptions, {
        message: 'Request deduplicated',
      });
      return this.ongoingRequests.get(requestKey)!;
    }

    if (this.currentMethod === 'GET' && !options.disableCache) {
      const cachedData = getCache(url);
      if (cachedData) {
        this.logRequest('CACHE_HIT', url, fetchOptions, cachedData);
        return cachedData as T;
      }
    }

    const requestPromise = (async (): Promise<T> => {
      const requestOptions: RequestInit = {
        ...fetchOptions,
        headers: {
          ...this.defaultHeaders,
          ...fetchOptions.headers,
        },
      };

      let lastError: Error | undefined;

      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          const response = await fetch(url, {
            ...requestOptions,
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw await this.handleHttpError(response);
          }

          if (options.parseAsText) {
            return (await response.text()) as unknown as T;
          }

          const data = await response.json();
          if (this.currentMethod === 'GET' && !options.disableCache) {
            setCache(url, data);
          }
          this.logRequest('SUCCESS', url, requestOptions, data);
          return data;
        } catch (error) {
          lastError = error as Error;
          this.logRequest('ERROR', url, requestOptions, error);

          if (this.shouldNotRetry(error as Error) || attempt === retries) {
            throw error;
          }

          await this.delay(retryDelay * (attempt + 1));
        }
      }
      throw lastError!;
    })();

    this.ongoingRequests.set(requestKey, requestPromise);

    try {
      return await requestPromise;
    } finally {
      this.ongoingRequests.delete(requestKey);
    }
  }

  /**
   * Handle HTTP errors and create meaningful error messages
   */
  private async handleHttpError(response: Response): Promise<ApiError> {
    return await createApiError(
      response,
      this.getCurrentEndpoint(),
      this.getCurrentMethod()
    );
  }

  /**
   * Get current endpoint for error context
   */
  private getCurrentEndpoint(): string {
    return this.currentEndpoint || 'unknown';
  }

  /**
   * Get current method for error context
   */
  private getCurrentMethod(): string {
    return this.currentMethod || 'unknown';
  }

  /**
   * Log the request status
   */
  private logRequest(
    status: 'SUCCESS' | 'ERROR' | 'CACHE_HIT' | 'DEDUPLICATED',
    url: string,
    options: RequestInit,
    context: any
  ): void {
    if (process.env.NODE_ENV === 'development') {
      // Request logging removed
    }
  }

  /**
   * Determine if a request should not be retried based on the error
   */
  private shouldNotRetry(error: Error): boolean {
    if (isApiError(error)) {
      // Don't retry on client-side errors (4xx)
      return error.status >= 400 && error.status < 500;
    }
    // Don't retry on abort errors
    return error.name === 'AbortError';
  }

  /**
   * Delay execution for a given time
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }



  /**
   * GET request helper
   */
  protected async get<T>(
    endpoint: string,
    params?: Record<string, any>,
    options?: RequestOptions
  ): Promise<T> {
    const url = params ? `${endpoint}?${this.buildQueryString(params)}` : endpoint;
    return this.makeRequest<T>(url, { ...options, method: 'GET' });
  }

  /**
   * POST request helper
   */
  protected async post<T>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request helper
   */
  protected async put<T>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request helper
   */
  protected async delete<T>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Build query string from parameters
   */
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, String(item)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    
    return searchParams.toString();
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(params: Record<string, any>, required: string[]): void {
    const missing = required.filter(key => 
      params[key] === undefined || params[key] === null || params[key] === ''
    );
    
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Format error message for user display
   */
  protected formatErrorMessage(error: unknown): string {
    const normalizedError = normalizeError(error as ErrorLike);
    return normalizedError.message;
  }
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a service instance with specific endpoint prefix
 */
export function createServiceInstance(endpointPrefix: string): BaseService {
  return new (class extends BaseService {
    constructor() {
      super();
    }
    
    protected getEndpoint(path: string): string {
      return `${endpointPrefix}${path}`;
    }
  })();
}

/**
 * Format API response for consistent handling
 */
export function formatApiResponse<T>(response: any): ApiResponse<T> {
  return {
    success: response.success ?? true,
    data: response.data ?? response,
    message: response.message,
    error: response.error,
  };
}

/**
 * Handle pagination parameters
 */
export function handlePagination(params: PaginationParams = {}): PaginationParams {
  return {
    page: Math.max(1, params.page || 1),
    page_size: Math.min(100, Math.max(1, params.page_size || 20)),
  };
}

// Removed createApiError function - now using standardized error utilities

export default BaseService;