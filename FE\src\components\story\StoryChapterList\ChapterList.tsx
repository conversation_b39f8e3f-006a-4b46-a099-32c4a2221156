'use client';

import React from 'react';
import { Chapter } from '@/types/story';
import { SelectableList } from '@/components/ui/SelectableList';
import { ChapterStatusBadges } from '@/components/ui/StatusBadge';
import { cleanChapterTitle } from '@/utils/chapterUtils';
import { LazyWrapper, ChapterListSkeleton } from '@/components/ui/LazyLoading';
import { AccessibleAnnouncement } from '@/components/ui/AccessibilityEnhanced';

interface ChapterListProps {
  storyId: string;
  chapters: Chapter[];
  loading: boolean;
  selectedChapters: Set<string>;
  selectionMode: 'scraping' | 'enhancement' | null;
  onSelectChapter: (chapterId: string) => void;
  onSelectAll: (mode: 'scraping' | 'enhancement') => void;
  onDeselectAll: () => void;
}

export const ChapterList: React.FC<ChapterListProps> = ({ 
  storyId, 
  chapters, 
  loading, 
  selectedChapters, 
  selectionMode, 
  onSelectChapter, 
  onSelectAll, 
  onDeselectAll 
}) => {
  const isChapterSelectable = (chapter: Chapter) => {
    return (
      (selectionMode === 'scraping' && !chapter.is_scraped) ||
      (selectionMode === 'enhancement' && chapter.is_scraped && !chapter.is_enhanced)
    );
  };

  // Accessibility announcements
  const getSelectionAnnouncement = () => {
    if (!selectionMode) return '';
    const count = selectedChapters.size;
    const mode = selectionMode === 'scraping' ? 'scraping' : 'enhancement';
    return count > 0 ? `${count} chapters selected for ${mode}` : `Selection mode: ${mode}`;
  };

  const headerTitle = selectionMode === 'scraping' 
    ? 'Select chapters to scrape' 
    : 'Select chapters to enhance';

  return (
    <>
      <AccessibleAnnouncement message={getSelectionAnnouncement()} />
      
      <SelectableList.Root
        selectionMode={selectionMode}
        selectedItems={selectedChapters}
        onSelectItem={onSelectChapter}
        isItemSelectable={isChapterSelectable}
      >
        <SelectableList.Header
          title={headerTitle}
          onSelectAll={() => selectionMode && onSelectAll(selectionMode)}
          onDeselectAll={onDeselectAll}
        />
        
        <div 
          role="list"
          aria-label={`Chapter list - ${chapters.length} chapters`}
          className="px-2 sm:px-0"
        >
          <SelectableList.Container
            loading={loading}
            emptyMessage="No chapters found."
            layout="grid"
            gridCols={2}
          >
          {loading ? (
            <ChapterListSkeleton count={6} />
          ) : (
            chapters.map((chapter, index) => (
              <LazyWrapper
                key={chapter.id}
                threshold={0.1}
                rootMargin="100px"
                minHeight="3rem"
              >
                <SelectableList.Item
                  id={chapter.id}
                  item={chapter}
                >
                  <SelectableList.ItemContent
                    href={`/stories/${storyId}/${chapter.chapter_number}`}
                    className="flex items-center gap-2 sm:gap-3 min-h-[2.5rem] sm:min-h-[3rem] w-full"
                  >
                    <span className="flex-grow font-medium text-sm sm:text-base truncate">
                      {cleanChapterTitle(chapter.title)}
                    </span>
                    <div className="flex-shrink-0">
                      <ChapterStatusBadges
                        isScraped={chapter.is_scraped}
                        isEnhanced={chapter.is_enhanced}
                        size="sm"
                        className="scale-90 sm:scale-100"
                      />
                    </div>
                  </SelectableList.ItemContent>
                </SelectableList.Item>
              </LazyWrapper>
            ))
          )}
          </SelectableList.Container>
        </div>
      </SelectableList.Root>
    </>
  );
};
