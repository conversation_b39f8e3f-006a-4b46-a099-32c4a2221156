import { BaseService } from './BaseService';
import {
  WorkflowType,
  ScrapingConfiguration,
  WorkflowRecommendation,
  WorkflowInfo
} from '@/types/unifiedScraping';

/**
 * Service for unified scraping workflow management
 */
class UnifiedScrapingService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Analyze URL and recommend workflow
   */
  async analyzeUrl(url: string): Promise<WorkflowRecommendation> {
    try {
      // Basic URL validation
      if (!url || !url.includes('webtruyen.diendantruyen.com')) {
        throw new Error('Invalid URL. Only webtruyen.diendantruyen.com URLs are supported');
      }

      // For now, always recommend simple workflow
      // This can be enhanced with actual API call to backend
      return {
        recommendedWorkflow: 'simple',
        confidence: 0.8,
        reasons: [
          'URL appears to be a standard story page',
          'Simple workflow is sufficient for most cases'
        ],
        estimatedBenefits: [
          'Faster processing',
          'Lower resource usage',
          'Reliable results'
        ]
      };
    } catch (error) {
      throw new Error(`Failed to analyze URL: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Validate scraping configuration
   */
  validateConfiguration(config: ScrapingConfiguration): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate URL
    if (!config.url) {
      errors.push('URL is required');
    } else if (!config.url.includes('webtruyen.diendantruyen.com')) {
      errors.push('Only webtruyen.diendantruyen.com URLs are supported');
    }

    // Validate workflow-specific settings
    if (config.workflow === 'simple') {
      if (config.maxPages && (config.maxPages < 1 || config.maxPages > 100)) {
        errors.push('Max pages must be between 1 and 100');
      }
    }

    if (config.workflow === 'hierarchical') {
      if (config.maxConcurrent && (config.maxConcurrent < 1 || config.maxConcurrent > 10)) {
        errors.push('Max concurrent requests must be between 1 and 10');
      }
      if (config.rateLimit && (config.rateLimit < 0.5 || config.rateLimit > 10)) {
        errors.push('Rate limit must be between 0.5 and 10 seconds');
      }
    }

    // Validate retry settings
    if (config.maxRetries && (config.maxRetries < 0 || config.maxRetries > 10)) {
      errors.push('Max retries must be between 0 and 10');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get workflow information
   */
  getWorkflowInfo(workflow: WorkflowType): WorkflowInfo {
    const workflowInfoMap: Record<WorkflowType, WorkflowInfo> = {
      simple: {
        id: 'simple',
        name: 'Simple Scraping',
        description: 'Basic scraping workflow for standard story extraction',
        icon: 'Download',
        features: [
          'Fast and reliable',
          'Low resource usage',
          'Automatic chapter detection',
          'Basic error handling'
        ],
        limitations: [
          'Limited customization',
          'No advanced content processing',
          'Sequential processing only'
        ],
        estimatedTime: '2-5 minutes',
        resourceUsage: 'low',
        complexity: 'beginner',
        bestFor: [
          'Quick story extraction',
          'Standard web novels',
          'First-time users'
        ]
      },
      hierarchical: {
        id: 'hierarchical',
        name: 'Hierarchical Scraping',
        description: 'Advanced workflow with parallel processing and content enhancement',
        icon: 'Network',
        features: [
          'Parallel processing',
          'Advanced content extraction',
          'Intelligent retry logic',
          'Content enhancement',
          'Detailed progress tracking'
        ],
        limitations: [
          'Higher resource usage',
          'More complex configuration',
          'Longer setup time'
        ],
        estimatedTime: '5-15 minutes',
        resourceUsage: 'high',
        complexity: 'advanced',
        bestFor: [
          'Large stories',
          'Complex content structures',
          'Advanced users',
          'Batch processing'
        ]
      }
    };

    return workflowInfoMap[workflow];
  }
}

// Create and export service instance
const unifiedScrapingService = new UnifiedScrapingService();
export default unifiedScrapingService;

// Export individual functions for convenience
export const analyzeUrl = (url: string) => unifiedScrapingService.analyzeUrl(url);
export const validateConfiguration = (config: ScrapingConfiguration) => unifiedScrapingService.validateConfiguration(config);
export const getWorkflowInfo = (workflow: WorkflowType) => unifiedScrapingService.getWorkflowInfo(workflow);