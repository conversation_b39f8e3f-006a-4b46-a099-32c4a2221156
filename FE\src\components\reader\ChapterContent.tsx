'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Chapter } from '@/types/story';
import { FONT_FAMILIES } from '@/hooks/useReaderSettings';

type Theme = 'dark' | 'light' | 'sepia';
type FontFamily = 'inter' | 'georgia' | 'times' | 'merriweather' | 'source-serif' | 'noto-serif' | 'roboto' | 'jetbrains-mono';

interface ChapterContentProps {
  chapter: Chapter;
  fontSize: number;
  lineHeight: number;
  theme: Theme;
  fontFamily: FontFamily;
}

const ChapterContent: React.FC<ChapterContentProps> = ({
  chapter,
  fontSize,
  lineHeight,
  theme,
  fontFamily
}) => {
  // DEBUG LOGS - Validate font application
  console.log('🔍 ChapterContent Debug:', {
    fontFamily,
    cssFamily: FONT_FAMILIES[fontFamily]?.cssFamily,
    timestamp: new Date().toISOString()
  });
  
  // Theme-based styling
  const getThemeStyles = () => {
    switch (theme) {
      case 'light':
        return {
          cardClass: 'bg-white border-gray-200',
          textColor: '#111827',
          backgroundColor: '#ffffff'
        };
      case 'sepia':
        return {
          cardClass: 'bg-amber-50 border-amber-200',
          textColor: '#92400e',
          backgroundColor: '#fefdf8'
        };
      case 'dark':
      default:
        return {
          cardClass: 'bg-zinc-800/50 border-zinc-700/50',
          textColor: '#f3f4f6',
          backgroundColor: 'transparent'
        };
    }
  };
  
  const themeStyles = getThemeStyles();
  const formatChapterContent = (content: string) => {
    if (!content) return 'Content not available';

    // Clean up the content and format paragraphs for Vietnamese text
    let formattedContent = content
      // Normalize line endings
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')

      // Remove excessive whitespace but preserve intentional spacing
      .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
      .replace(/\n[ \t]+/g, '\n') // Remove leading whitespace on lines
      .replace(/[ \t]+\n/g, '\n') // Remove trailing whitespace on lines

      // Handle common Vietnamese punctuation and spacing
      .replace(/\s*([.!?])\s*/g, '$1 ') // Normalize punctuation spacing
      .replace(/\s*([,;:])\s*/g, '$1 ') // Normalize comma/semicolon spacing
      .replace(/\s*([""''„"])\s*/g, ' "') // Handle Vietnamese quotes
      .replace(/([""''„"])\s*/g, '" ') // Handle closing quotes

      // Convert line breaks to paragraph breaks
      .replace(/\n\s*\n\s*/g, '\n\n') // Normalize paragraph breaks
      .split('\n\n') // Split into paragraphs
      .map(paragraph => paragraph.trim()) // Trim each paragraph
      .filter(paragraph => paragraph.length > 0) // Remove empty paragraphs
      .map(paragraph => {
        // Add proper paragraph styling with Vietnamese text considerations
        const isDialogue = paragraph.match(/^["\']/); // Check if it's dialogue
        const isTitle = paragraph.length < 100 && paragraph.match(/^[A-Z]/);

        let style = 'margin-bottom: 1.5em; line-height: 1.8;';

        if (isDialogue) {
          style += ' text-indent: 1em; font-style: italic;'; // Less indent for dialogue
        } else if (isTitle) {
          style += ' text-align: center; font-weight: bold; margin-bottom: 2em; text-indent: 0;'; // Center titles
        } else {
          style += ' text-indent: 2em;'; // Standard paragraph indent
        }

        return `<p style="${style}">${paragraph}</p>`;
      })
      .join('');

    return formattedContent || '<p style="text-align: center; color: #9ca3af;">Content not available</p>';
  };

  return (
    <Card className={`transition-all duration-300 ${themeStyles.cardClass}`}>
      <CardContent className="p-3 sm:p-4 lg:p-6">
        <div
          className="prose prose-lg max-w-none"
          style={{
            fontSize: `${fontSize}px`,
            lineHeight: lineHeight,
            color: themeStyles.textColor,
            fontFamily: FONT_FAMILIES[fontFamily].cssFamily,
          }}
        >
          <div
            className="chapter-content vietnamese-text"
            style={{
              wordBreak: 'break-word',
              textAlign: 'justify',
              textJustify: 'inter-word',
              hyphens: 'auto',
              WebkitHyphens: 'auto',
              MozHyphens: 'auto',
              msHyphens: 'auto',
              // Better Vietnamese text rendering
              fontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
              WebkitFontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
              // Improve readability
              letterSpacing: '0.01em',
              wordSpacing: '0.05em',
              // Smooth text rendering
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale',
              textRendering: 'optimizeLegibility',
              // Mobile-specific optimizations
              touchAction: 'manipulation',
              userSelect: 'text',
              WebkitUserSelect: 'text',
              // Better mobile scrolling
              overflowWrap: 'break-word',
              // Prevent zoom on double tap for better reading experience
              WebkitTouchCallout: 'none',
              WebkitTapHighlightColor: 'transparent'
            }}
            dangerouslySetInnerHTML={{
              __html: formatChapterContent(chapter.enhanced_content || chapter.original_content || 'Content not available')
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ChapterContent;