import { Story, Chapter, StoriesResponse, ChaptersResponse, StoryWithChapters } from '@/types/story';
import { BaseService, API_ENDPOINTS, PaginationParams, handlePagination } from './BaseService';

// Stories and Chapters Service
class StoriesService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Fetch all stories with pagination
   */
  async getStories(params: PaginationParams = {}): Promise<StoriesResponse> {
    const paginationParams = handlePagination(params);
    
    try {
      return await this.get(API_ENDPOINTS.STORIES, paginationParams);
    } catch (error) {
      throw new Error(`Failed to fetch stories: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get detailed information about a specific story
   */
  async getStoryById(storyId: string): Promise<StoryWithChapters> {
    this.validateRequired({ storyId }, ['storyId']);
    
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/${storyId}`);
    } catch (error) {
      throw new Error(`Failed to fetch story: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get paginated list of chapters for a specific story
   */
  async getChaptersByStoryId(
    storyId: string,
    params: PaginationParams & {
      enhanced_only?: boolean;
      scraped_only?: boolean;
      disableCache?: boolean;
    } = {}
  ): Promise<ChaptersResponse> {
    this.validateRequired({ storyId }, ['storyId']);
    
    const { enhanced_only, scraped_only, disableCache, ...paginationParams } = params;
    const queryParams = {
      ...handlePagination(paginationParams),
      ...(enhanced_only !== undefined && { enhanced_only }),
      ...(scraped_only !== undefined && { scraped_only }),
    };
    
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/${storyId}/chapters`, queryParams, {
        disableCache
      });
    } catch (error) {
      throw new Error(`Failed to fetch chapters: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get specific chapter content by chapter number
   */
  async getChapterByNumber(storyId: string, chapterNumber: number): Promise<Chapter> {
    this.validateRequired({ storyId, chapterNumber }, ['storyId', 'chapterNumber']);
    
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/${storyId}/chapters/${chapterNumber}`);
    } catch (error) {
      throw new Error(`Failed to fetch chapter: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get specific chapter content by chapter ID
   */
  async getChapterById(chapterId: string): Promise<Chapter> {
    this.validateRequired({ chapterId }, ['chapterId']);
    
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/chapters/${chapterId}`);
    } catch (error) {
      throw new Error(`Failed to fetch chapter: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Advanced search for stories with complex filtering
   */
  async advancedSearch(searchRequest: {
    search_term?: string;
    filters?: {
      author?: string;
      status?: string;
      genres?: string[];
      min_chapters?: number;
      max_chapters?: number;
    };
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
    page?: number;
    page_size?: number;
  }): Promise<StoriesResponse> {
    try {
      return await this.post(`${API_ENDPOINTS.STORIES}/advanced-search`, searchRequest);
    } catch (error) {
      throw new Error(`Advanced search failed: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get story suggestions based on various criteria
   */
  async getStorySuggestions(params: {
    limit?: number;
    category?: 'popular' | 'recent' | 'recommended';
  } = {}): Promise<{ success: boolean; suggestions: any[] }> {
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/suggestions`, params);
    } catch (error) {
      throw new Error(`Failed to get suggestions: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Compare original and enhanced content for chapters
   */
  async compareContent(request: {
    chapter_ids: string[];
    comparison_type: 'side_by_side';
  }): Promise<{ success: boolean; comparisons: any[] }> {
    this.validateRequired(request, ['chapter_ids']);
    
    try {
      return await this.post(`${API_ENDPOINTS.STORIES}/compare-content`, request);
    } catch (error) {
      throw new Error(`Content comparison failed: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get story information by URL
   */
  async getStoryByUrl(url: string): Promise<Story> {
    this.validateRequired({ url }, ['url']);
    
    try {
      return await this.get(`${API_ENDPOINTS.STORIES}/story-by-url`, { url });
    } catch (error) {
      throw new Error(`Failed to get story by URL: ${this.formatErrorMessage(error)}`);
    }
  }
}

// Service instance
export const storiesService = new StoriesService();

// Legacy function exports for backward compatibility
export const fetchStories = async (page: number = 1, pageSize: number = 20): Promise<StoriesResponse> => {
  return storiesService.getStories({ page, page_size: pageSize });
};

export const fetchStoryById = async (storyId: string): Promise<StoryWithChapters> => {
  return storiesService.getStoryById(storyId);
};

export const fetchChaptersByStoryId = async (
  storyId: string,
  page: number = 1,
  pageSize: number = 20,
  filters?: {
    enhanced_only?: boolean;
    scraped_only?: boolean;
  },
  options?: {
    disableCache?: boolean;
  }
): Promise<ChaptersResponse> => {
  return storiesService.getChaptersByStoryId(storyId, {
    page,
    page_size: pageSize,
    ...filters,
    ...options,
  });
};

export const fetchChapterByNumber = async (storyId: string, chapterNumber: number): Promise<Chapter> => {
  return storiesService.getChapterByNumber(storyId, chapterNumber);
};

export const fetchChapterById = async (chapterId: string): Promise<Chapter> => {
  return storiesService.getChapterById(chapterId);
};

// Export new methods
export const advancedSearchStories = async (searchRequest: {
  search_term?: string;
  filters?: {
    author?: string;
    status?: string;
    genres?: string[];
    min_chapters?: number;
    max_chapters?: number;
  };
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  page_size?: number;
}): Promise<StoriesResponse> => {
  return storiesService.advancedSearch(searchRequest);
};

export const getStorySuggestions = async (params: {
  limit?: number;
  category?: 'popular' | 'recent' | 'recommended';
} = {}): Promise<{ success: boolean; suggestions: any[] }> => {
  return storiesService.getStorySuggestions(params);
};

export const compareChapterContent = async (request: {
  chapter_ids: string[];
  comparison_type: 'side_by_side';
}): Promise<{ success: boolean; comparisons: any[] }> => {
  return storiesService.compareContent(request);
};

export const getStoryByUrl = async (url: string): Promise<Story> => {
  return storiesService.getStoryByUrl(url);
};