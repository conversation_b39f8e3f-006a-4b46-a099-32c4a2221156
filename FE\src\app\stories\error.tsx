'use client';

import { useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, Home, Copy, BookOpen } from 'lucide-react';
import { createAppError, handleError, getUserFriendlyMessage } from '@/utils/errorHandling';
import { toast } from 'sonner';
import { AppErrorType } from '@/types/errors';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function StoriesErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // Log the error using our standardized error handling
    const appError = createAppError(
      error.message,
      AppErrorType.CLIENT,
      true,
      'An error occurred on the stories page',
      {
        digest: error.digest,
        stack: error.stack,
        route: '/stories',
        errorBoundary: 'StoriesRoute'
      }
    );
    
    handleError(appError);
  }, [error]);

  const handleCopyError = () => {
    const errorDetails = {
      message: error.message,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      route: '/stories',
      userAgent: navigator.userAgent
    };
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => toast.success('Error details copied to clipboard'))
      .catch(() => toast.error('Failed to copy error details'));
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoToScrape = () => {
    window.location.href = '/scrape';
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="text-center space-y-6">
            <BookOpen className="h-16 w-16 text-red-500 mx-auto" />
            
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Stories Page Error</h1>
              <p className="text-muted-foreground">
                Unable to load the stories page
              </p>
            </div>

            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error.message || 'An unexpected error occurred'}
              </AlertDescription>
            </Alert>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={reset} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
              
              <Button onClick={handleReload} variant="outline" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Reload Page
              </Button>
              
              <Button onClick={handleGoToScrape} variant="outline" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Scrape Stories
              </Button>
              
              <Button onClick={handleGoHome} variant="outline" className="flex items-center gap-2">
                <Home className="h-4 w-4" />
                Go Home
              </Button>
            </div>

            <div className="pt-4 border-t">
              <Button 
                onClick={handleCopyError} 
                variant="ghost" 
                size="sm"
                className="flex items-center gap-2 text-muted-foreground"
              >
                <Copy className="h-3 w-3" />
                Copy Error Details
              </Button>
              
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                    Technical Details (Development)
                  </summary>
                  <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-h-40">
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}