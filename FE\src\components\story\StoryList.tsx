'use client';

import { useEffect, useState } from 'react';
import { fetchStories, advancedSearchStories } from '@/services/storyService';
import { Story } from '@/types/story';
import StoryCard from './StoryCard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '../ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import EnhancedPagination from '../ui/enhanced-pagination';
import { EnhancedSearchInput } from '../ui/EnhancedUI';
import { Filter, X, BookOpen, Search, Plus, Sparkles } from 'lucide-react';

const StoryList = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [authorFilter, setAuthorFilter] = useState<string>('all');
  const [availableAuthors, setAvailableAuthors] = useState<string[]>([]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const loadStories = async (pageNum: number = 1, searchQuery?: string, status?: string, author?: string) => {
    try {
      setLoading(true);
      const query = searchQuery !== undefined ? searchQuery : debouncedSearchTerm;
      const currentStatus = status !== undefined ? status : statusFilter;
      const currentAuthor = author !== undefined ? author : authorFilter;
      
      let response;
      if (query.trim() || currentStatus !== 'all' || currentAuthor !== 'all') {
        const filters: any = {};
        if (currentStatus !== 'all') filters.status = currentStatus;
        if (currentAuthor !== 'all') filters.author = currentAuthor;
        
        response = await advancedSearchStories({
          search_term: query || undefined,
          filters: Object.keys(filters).length > 0 ? filters : undefined,
          page: pageNum,
          page_size: 12
        });
      } else {
        response = await fetchStories(pageNum, 12);
      }

      setStories(response.data);
      setCurrentPage(pageNum);
      setTotalPages(response.pagination.total_pages);
      setTotalItems(response.pagination.total_items);
      setError(null);
      
      // Extract unique authors for filter dropdown
      if (pageNum === 1) {
        const authors = Array.from(new Set(response.data.map(story => story.author).filter(Boolean))) as string[];
        setAvailableAuthors(authors);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stories');
    } finally {
      setLoading(false);
    }
  };

  // Load stories when search term or filters change
  useEffect(() => {
    loadStories(1, debouncedSearchTerm, statusFilter, authorFilter);
  }, [debouncedSearchTerm, statusFilter, authorFilter]);

  // Helper functions for filter management
  const clearAllFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setAuthorFilter('all');
  };

  const hasActiveFilters = debouncedSearchTerm || statusFilter !== 'all' || authorFilter !== 'all';



  if (loading && stories.length === 0) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="border border-border/40 bg-card rounded-lg overflow-hidden">
            {/* Image skeleton with fixed height */}
            <Skeleton className="h-32 w-full rounded-t-lg" />
            
            <CardContent className="p-3 space-y-2">
              {/* Title skeleton */}
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-4/5" />
              
              {/* Author and chapter count skeleton */}
              <div className="flex items-center justify-between">
                <Skeleton className="h-3 w-20" />
                <Skeleton className="h-3 w-16" />
              </div>
              
              {/* Genre badges skeleton */}
              <div className="flex gap-1">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-10" />
              </div>
              
              {/* Footer skeleton */}
              <div className="flex items-center justify-between pt-1">
                <Skeleton className="h-3 w-8" />
                <Skeleton className="h-3 w-12" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center mt-8">
        <Card className="border-destructive/50 bg-destructive/10">
          <CardContent className="p-6">
            <h3 className="text-destructive font-semibold mb-2">Error Loading Data</h3>
            <p className="text-destructive/80 mb-4">{error}</p>
            <Button
              onClick={() => loadStories(1)}
              variant="destructive"
              className="transition-all duration-200 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (stories.length === 0) {
    return (
      <div className="text-center mt-8">
        <Card className="border-dashed border-2 border-muted-foreground/25">
          <CardContent className="p-12">
            {debouncedSearchTerm ? (
              <>
                {/* Search Empty State */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 rounded-full bg-muted/50 flex items-center justify-center">
                      <Search className="w-12 h-12 text-muted-foreground/60" />
                    </div>
                  </div>
                  <div className="relative z-10 pt-24">
                    <div className="w-3 h-3 bg-muted-foreground/30 rounded-full mx-auto animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-muted-foreground/20 rounded-full mx-auto mt-1 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-1 h-1 bg-muted-foreground/10 rounded-full mx-auto mt-1 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">No Stories Found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto leading-relaxed">
                  We couldn&apos;t find any stories matching <span className="font-medium text-foreground">&quot;{debouncedSearchTerm}&quot;</span>. 
                  Try adjusting your search terms or filters.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button 
                    onClick={() => setSearchTerm('')} 
                    variant="outline" 
                    className="gap-2 transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md hover:border-primary/50 hover:bg-primary/5"
                  >
                    <X className="w-4 h-4 transition-transform duration-200 group-hover:rotate-90" />
                    Clear Search
                  </Button>
                  <Button 
                    onClick={clearAllFilters} 
                    variant="default" 
                    className="gap-2 transition-all duration-200 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
                  >
                    <Search className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
                    Reset All Filters
                  </Button>
                </div>
              </>
            ) : (
              <>
                {/* No Stories Empty State */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                      <BookOpen className="w-12 h-12 text-primary" />
                    </div>
                  </div>
                  <div className="relative z-10 pt-24">
                    <Sparkles className="w-6 h-6 text-accent mx-auto animate-pulse" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3">Welcome to Your Story Library</h3>
                <p className="text-muted-foreground mb-8 max-w-lg mx-auto leading-relaxed">
                  Your story collection is empty, but that&apos;s about to change! Start by scraping your first story 
                  and begin building your personal digital library.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    asChild 
                    size="lg" 
                    className="gap-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 active:scale-95 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 group"
                  >
                    <a href="/scrape">
                      <Plus className="w-5 h-5 transition-transform duration-200 group-hover:rotate-90" />
                      Scrape Your First Story
                    </a>
                  </Button>
                  <Button 
                    asChild 
                    variant="outline" 
                    size="lg" 
                    className="gap-2 transition-all duration-300 hover:scale-105 active:scale-95 hover:shadow-md hover:border-primary/50 hover:bg-primary/5 group"
                  >
                    <a href="/docs" target="_blank" rel="noopener noreferrer">
                      <BookOpen className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
                      Learn How It Works
                    </a>
                  </Button>
                </div>
                <div className="mt-8 pt-6 border-t border-muted">
                  <p className="text-sm text-muted-foreground">
                    💡 <span className="font-medium">Pro tip:</span> You can scrape stories from various sources and enhance them with AI for better readability.
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mt-8">
      {/* Search Input */}
      <div className="mb-6">
        <EnhancedSearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search stories by title, author, or description..."
          onClear={() => setSearchTerm('')}
        />
      </div>

      {/* Filter Controls */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col xs:flex-row gap-3 flex-1">
            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger 
                  className="w-[140px] focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  aria-label="Filter stories by status"
                >
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ongoing">Ongoing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="hiatus">Hiatus</SelectItem>
                  <SelectItem value="dropped">Dropped</SelectItem>
                  <SelectItem value="unknown">Unknown</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Author Filter */}
            <div className="flex items-center gap-2">
              <Select value={authorFilter} onValueChange={setAuthorFilter}>
                <SelectTrigger 
                  className="w-[160px] focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  aria-label="Filter stories by author"
                >
                  <SelectValue placeholder="Author" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Authors</SelectItem>
                  {availableAuthors.map((author) => (
                    <SelectItem key={author} value={author}>
                      {author}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters and Clear Button */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <div className="flex flex-wrap gap-2">
                {debouncedSearchTerm && (
                  <Badge variant="secondary" className="text-xs transition-all duration-200 hover:bg-secondary/80 group">
                    Search: {debouncedSearchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-1 hover:text-destructive transition-all duration-200 hover:scale-110 active:scale-95"
                      aria-label="Clear search"
                    >
                      <X className="h-3 w-3 transition-transform duration-200 group-hover:rotate-90" />
                    </button>
                  </Badge>
                )}
                {statusFilter !== 'all' && (
                  <Badge variant="secondary" className="text-xs transition-all duration-200 hover:bg-secondary/80 group">
                    Status: {statusFilter}
                    <button
                      onClick={() => setStatusFilter('all')}
                      className="ml-1 hover:text-destructive transition-all duration-200 hover:scale-110 active:scale-95"
                      aria-label="Clear status filter"
                    >
                      <X className="h-3 w-3 transition-transform duration-200 group-hover:rotate-90" />
                    </button>
                  </Badge>
                )}
                {authorFilter !== 'all' && (
                  <Badge variant="secondary" className="text-xs transition-all duration-200 hover:bg-secondary/80 group">
                    Author: {authorFilter}
                    <button
                      onClick={() => setAuthorFilter('all')}
                      className="ml-1 hover:text-destructive transition-all duration-200 hover:scale-110 active:scale-95"
                      aria-label="Clear author filter"
                    >
                      <X className="h-3 w-3 transition-transform duration-200 group-hover:rotate-90" />
                    </button>
                  </Badge>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md hover:border-primary/50 hover:bg-primary/5"
              >
                Clear All
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Stories count and pagination info */}
      {totalItems > 0 && (
        <div className="flex justify-between items-center mb-6">
          <p className="text-muted-foreground text-sm">
            Showing {((currentPage - 1) * 12) + 1}-{Math.min(currentPage * 12, totalItems)} of {totalItems} stories
            {debouncedSearchTerm && ` matching "${debouncedSearchTerm}"`}
          </p>
          <p className="text-muted-foreground text-sm">
            Page {currentPage} / {totalPages}
          </p>
        </div>
      )}

      <div 
        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        role="grid"
        aria-label={`Stories grid showing ${stories.length} stories`}
      >
        {stories.map((story, index) => (
          <div
            key={story.id}
            className="animate-fade-in opacity-0"
            style={{
              animationDelay: `${index * 100}ms`,
              animationFillMode: 'forwards'
            }}
            role="gridcell"
          >
            <StoryCard story={story} />
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8">
          <EnhancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => loadStories(page, debouncedSearchTerm, statusFilter, authorFilter)}
            loading={loading}
            className="justify-center"
          />
        </div>
      )}
    </div>
  );
};

export default StoryList;