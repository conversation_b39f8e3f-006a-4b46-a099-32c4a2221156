import { BaseService, API_ENDPOINTS } from './BaseService';

// ============================================================================
// Request/Response Types
// ============================================================================

// Story Scraping Types
export interface ScrapeRequest {
  story_url: string;
  max_pages?: number;
}

export interface ScrapeResponse {
  success: boolean;
  story_id: string;
  title: string;
  total_pages: number;
  total_chapters: number;
  pages_scraped: number;
  chapters_created: number;
  message: string;
  data?: {
    story: any;
    pages: any[];
    chapters: any[];
  };
}

// Story Pages Types
export interface StoryPagesResponse {
  story_id: string;
  total_pages: number;
  pages: PageInfo[];
}

export interface PageInfo {
  page_id: string;
  page_number: number;
  page_url: string;
  total_chapters_on_page: number;
  chapter_urls: string[];
  is_scraped: boolean;
  created_at: string;
}

// Page Chapters Types
export interface PageChaptersResponse {
  page_id: string;
  page_number: number;
  page_url: string;
  total_chapters_on_page: number;
  chapters: ChapterInfo[];
}

export interface ChapterInfo {
  chapter_id: string;
  chapter_number: number;
  title: string;
  url: string;
  is_scraped: boolean;
  is_enhanced: boolean;
  word_count?: number;
  created_at: string;
}

// Chapter Content Types
export interface ChapterContentResponse {
  chapter_id: string;
  title: string;
  content: string;
  word_count?: number;
  scraped_at: string;
}

// Batch Scraping Types
export interface BatchScrapeRequest {
  story_id: string;
  chapter_ids: string[];
  max_concurrent?: number;
  rate_limit_delay?: number;
}

export interface BatchScrapeResponse {
  success: boolean;
  message: string;
  job_id: string;
  total_chapters_to_scrape: number;
  skipped_chapters: number;
}

export interface BatchChapterScrapeByUrlsRequest {
  chapter_urls: string[];
  story_id?: string;
  max_concurrent?: number;
  rate_limit_delay?: number;
}

export interface BatchChapterScrapeResponse {
  success: boolean;
  message: string;
  job_id: string;
  total_chapters: number;
  chapters: any[];
  failed_urls: string[];
}

// Job Progress Types
export interface ScrapeProgressUpdate {
  job_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress_percentage: number;
  total_items: number;
  completed_items: number;
  failed_items: number;
  current_item?: string;
  message?: string;
}

export interface JobStatusResponse {
  job_id: string;
  status: 'pending' | 'active' | 'completed' | 'error' | 'cancelled';
  message?: string;
  progress_percentage?: number;
  total_items?: number;
  completed_items?: number;
  progress?: {
    phase?: string;
    current_step?: number;
    total_steps?: number;
    message?: string;
    data?: any;
  };
  created_at?: string;
  updated_at?: string;
  error_details?: string;
  errors?: string[];
}

export interface TestScrapingResponse {
  success: boolean;
  message: string;
}

// Async Scraping Types
export interface AsyncScrapeRequest {
  story_url: string;
  max_pages?: number;
}

export interface AsyncScrapeResponse {
  success: boolean;
  job_id: string;
  message: string;
  websocket_url?: string;
  estimated_duration?: number;
  created_at: string;
}

// ============================================================================
// Scraping Service
// ============================================================================

class ScrapingService extends BaseService {
  constructor() {
    super();
  }

  // ========================================================================
  // Story Scraping Methods
  // ========================================================================

  /**
   * Perform complete scraping of a story using hierarchical workflow
   * POST /scrape
   */
  async scrapeStory(request: ScrapeRequest): Promise<ScrapeResponse> {
    this.validateRequired(request, ['story_url']);
    
    // Validate URL format
    if (!this.validateStoryUrl(request.story_url)) {
      throw new Error('Only webtruyen.diendantruyen.com URLs are supported');
    }
    
    // Validate max_pages range
    if (request.max_pages && (request.max_pages < 1 || request.max_pages > 100)) {
      throw new Error('max_pages must be between 1 and 100');
    }
    
    try {
      // Use longer timeout for scraping operations (5 minutes)
      return await this.makeRequest<ScrapeResponse>(
        API_ENDPOINTS.SCRAPING.SCRAPE,
        {
          method: 'POST',
          headers: this.defaultHeaders,
          body: JSON.stringify(request),
          timeout: 300000, // 5 minutes timeout
          retries: 3 // retry attempts
        }
      );
    } catch (error) {
      throw new Error(`Failed to scrape story: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Start asynchronous scraping of a story with real-time progress updates
   * POST /scrape-async
   */
  async scrapeStoryAsync(request: AsyncScrapeRequest): Promise<AsyncScrapeResponse> {
    this.validateRequired(request, ['story_url']);
    
    // Validate URL format
    if (!this.validateStoryUrl(request.story_url)) {
      throw new Error('Only webtruyen.diendantruyen.com URLs are supported');
    }
    
    // Validate max_pages range
    if (request.max_pages && (request.max_pages < 1 || request.max_pages > 100)) {
      throw new Error('max_pages must be between 1 and 100');
    }
    
    try {
      return await this.makeRequest<AsyncScrapeResponse>(
        API_ENDPOINTS.SCRAPING.ASYNC,
        {
          method: 'POST',
          headers: this.defaultHeaders,
          body: JSON.stringify(request),
          timeout: 30000, // 30 seconds timeout for job creation
          retries: 2
        }
      );
    } catch (error) {
      throw new Error(`Failed to start async scraping: ${this.formatErrorMessage(error)}`);
    }
  }

  // ========================================================================
  // Story & Pages Methods
  // ========================================================================

  /**
   * Get pages for a specific story
   * GET /story/{story_id}/pages
   */
  async getStoryPages(
    storyId: string, 
    pageNumber?: number
  ): Promise<StoryPagesResponse> {
    this.validateRequired({ storyId }, ['storyId']);
    
    try {
      const params = pageNumber ? { page_number: pageNumber } : undefined;
      return await this.get<StoryPagesResponse>(
        `${API_ENDPOINTS.SCRAPING.STORY_PAGES}/${storyId}/pages`,
        params
      );
    } catch (error) {
      throw new Error(`Failed to get story pages: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Get all chapters for a specific page
   * GET /page/{page_id}/chapters
   */
  async getPageChapters(pageId: string): Promise<PageChaptersResponse> {
    this.validateRequired({ pageId }, ['pageId']);
    
    try {
      return await this.get<PageChaptersResponse>(
        `${API_ENDPOINTS.SCRAPING.PAGE_CHAPTERS}/${pageId}/chapters`
      );
    } catch (error) {
      throw new Error(`Failed to get page chapters: ${this.formatErrorMessage(error)}`);
    }
  }

  // ========================================================================
  // Chapter Content Methods
  // ========================================================================

  /**
   * Scrape content for a specific chapter
   * POST /chapter/{chapter_id}/content
   */
  async scrapeChapterContent(chapterId: string): Promise<ChapterContentResponse> {
    this.validateRequired({ chapterId }, ['chapterId']);
    
    try {
      return await this.post<ChapterContentResponse>(
        `${API_ENDPOINTS.SCRAPING.CHAPTER_CONTENT}/${chapterId}/content`
      );
    } catch (error) {
      throw new Error(`Failed to scrape chapter content: ${this.formatErrorMessage(error)}`);
    }
  }

  // ========================================================================
  // Batch Scraping Methods
  // ========================================================================

  /**
   * Batch scrape multiple chapters by their IDs
   * POST /batch/scrape-chapters
   */
  async batchScrapeChapters(request: BatchScrapeRequest): Promise<BatchScrapeResponse> {
    this.validateRequired(request, ['story_id', 'chapter_ids']);
    
    if (!Array.isArray(request.chapter_ids) || request.chapter_ids.length === 0) {
      throw new Error('chapter_ids must be a non-empty array');
    }
    
    // No additional validation needed for simple URL list
    
    try {
      return await this.post<BatchScrapeResponse>(
        API_ENDPOINTS.SCRAPING.BATCH,
        request
      );
    } catch (error) {
      throw new Error(`Failed to start batch chapter scraping: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Batch scrape multiple chapters by their URLs
   * POST /batch/chapters/urls
   */
  async batchScrapeChaptersByUrls(request: BatchChapterScrapeByUrlsRequest): Promise<BatchChapterScrapeResponse> {
    this.validateRequired(request, ['chapter_urls']);
    
    if (!Array.isArray(request.chapter_urls) || request.chapter_urls.length === 0) {
      throw new Error('chapter_urls must be a non-empty array');
    }
    
    // No additional validation needed for simple URL list
    
    try {
      return await this.post<BatchChapterScrapeResponse>(
        API_ENDPOINTS.SCRAPING.BATCH_BY_URLS,
        request
      );
    } catch (error) {
      throw new Error(`Failed to start batch chapter scraping by URLs: ${this.formatErrorMessage(error)}`);
    }
  }

  // ========================================================================
  // Job Progress Methods
  // ========================================================================

  /**
   * Get progress of a scraping job
   * GET /job/{job_id}/progress
   */
  async getJobProgress(jobId: string): Promise<ScrapeProgressUpdate> {
    this.validateRequired({ jobId }, ['jobId']);
    
    try {
      return await this.get<ScrapeProgressUpdate>(
        `${API_ENDPOINTS.JOBS}/${jobId}/progress`
      );
    } catch (error) {
      throw new Error(`Failed to get job progress: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Cancel a running scraping job
   * POST /job/{job_id}/cancel
   */
  async cancelJob(jobId: string): Promise<{ success: boolean; message: string }> {
    this.validateRequired({ jobId }, ['jobId']);
    
    try {
      return await this.post<{ success: boolean; message: string }>(
        `${API_ENDPOINTS.JOBS}/${jobId}/cancel`
      );
    } catch (error) {
      throw new Error(`Failed to cancel job: ${this.formatErrorMessage(error)}`);
    }
  }

  // ========================================================================
  // Utility Methods
  // ========================================================================

  /**
   * Validate story URL format
   */
  validateStoryUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname === 'webtruyen.diendantruyen.com';
    } catch {
      return false;
    }
  }

  /**
   * Extract story slug from URL for display purposes
   */
  extractStorySlug(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
      return pathParts[pathParts.length - 1] || 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Get job status for polling fallback
   * GET /jobs/{job_id}/status
   */
  async getJobStatus(jobId: string, options: { parseAsText?: boolean } = {}): Promise<JobStatusResponse | string> {
    this.validateRequired({ jobId }, ['jobId']);
    
    try {
      const requestOptions = {
        method: 'GET',
        headers: this.defaultHeaders,
        cache: 'no-cache' as RequestCache,
        parseAsText: options.parseAsText || false,
        disableCache: true, // Disable internal cache for polling requests
      };

      return await this.makeRequest<JobStatusResponse | string>(
        `${API_ENDPOINTS.JOBS}/${jobId}/status`,
        requestOptions
      );
    } catch (error) {
      throw new Error(`Failed to get job status: ${this.formatErrorMessage(error)}`);
    }
  }

  /**
   * Format error message for user display
   * Override BaseService method to handle additional error formats
   */
  protected formatErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.detail) {
      return error.detail;
    }
    
    // Fall back to parent implementation
    return super.formatErrorMessage(error);
  }
}

// Export singleton instance
export const scrapingService = new ScrapingService();
export default scrapingService;