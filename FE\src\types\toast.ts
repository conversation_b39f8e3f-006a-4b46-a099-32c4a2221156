/**
 * Enhanced Toast Types for Batch Actions
 * Defines interfaces for toast notifications with refresh button functionality
 */

export interface RefreshButtonProps {
  /** Callback function when refresh button is clicked */
  onRefresh: () => void;
  /** Loading state of the refresh operation */
  loading?: boolean;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Accessibility label for screen readers */
  ariaLabel?: string;
}

export interface EnhancedToastProps {
  /** Main toast message */
  message: string;
  /** Toast type for styling */
  type: 'success' | 'error' | 'info' | 'warning';
  /** Whether to show refresh button */
  showRefreshButton?: boolean;
  /** Refresh button configuration */
  refreshButton?: RefreshButtonProps;
  /** Auto-dismiss duration in milliseconds (0 = no auto-dismiss) */
  duration?: number;
  /** Whether the toast can be manually dismissed */
  dismissible?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Unique identifier for the toast */
  id?: string;
}

export interface ToastHelperOptions {
  /** Auto-dismiss duration in milliseconds */
  duration?: number;
  /** Whether to show refresh button */
  showRefreshButton?: boolean;
  /** Refresh callback function */
  onRefresh?: () => void;
  /** Loading state for refresh button */
  refreshLoading?: boolean;
  /** Additional data to pass to toast */
  data?: Record<string, any>;
}

export interface BatchActionToastConfig {
  /** Configuration for batch scraping toasts */
  scraping: {
    loading: {
      message: string;
      duration?: number;
    };
    success: {
      message: string;
      duration: number;
      showRefreshButton: boolean;
    };
    error: {
      message: string;
      duration?: number;
    };
    cancelled: {
      message: string;
      duration?: number;
    };
  };
  /** Configuration for batch enhancement toasts */
  enhancement: {
    loading: {
      message: string;
      duration?: number;
    };
    success: {
      message: string;
      duration: number;
      showRefreshButton: boolean;
    };
    error: {
      message: string;
      duration?: number;
    };
    cancelled: {
      message: string;
      duration?: number;
    };
  };
}

export interface RefreshHandlerOptions {
  /** Story ID for cache clearing */
  storyId: string;
  /** Whether to preserve current page */
  preservePage?: boolean;
  /** Whether to preserve current filters */
  preserveFilters?: boolean;
  /** Callback for loading state changes */
  onLoadingChange?: (loading: boolean) => void;
  /** Callback for error handling */
  onError?: (error: Error) => void;
  /** Callback for success */
  onSuccess?: () => void;
}

export interface ToastNotificationState {
  /** Currently active toasts */
  activeToasts: Map<string, EnhancedToastProps>;
  /** Refresh operations in progress */
  refreshOperations: Set<string>;
  /** Last refresh timestamp for debouncing */
  lastRefreshTime: number;
  /** Debounce delay in milliseconds */
  debounceDelay: number;
}