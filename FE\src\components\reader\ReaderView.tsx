'use client';

import React from 'react';
import { Chapter } from '@/types/story';
import ChapterContent from './ChapterContent';
import ChapterStats from './ChapterStats';
import { useReaderSettings, FONT_FAMILIES } from '@/hooks/useReaderSettings';
import { useFontLoader } from '@/hooks/useFontLoader';

interface ReaderViewProps {
  chapter: Chapter;
}

const ReaderView: React.FC<ReaderViewProps> = ({ chapter }) => {
  const { fontSize, lineHeight, theme, fontFamily, isLoaded } = useReaderSettings();
  const { allFontsLoaded, isFontLoaded } = useFontLoader();

  // Get the actual font name for checking
  const currentFontName = FONT_FAMILIES[fontFamily]?.name || 'Inter';
  const currentFontLoaded = isFontLoaded(currentFontName);

  // DEBUG LOGS - Validate ReaderView props
  console.log('📖 ReaderView Debug:', {
    fontFamily,
    currentFontName,
    currentFontLoaded,
    allFontsLoaded,
    fontSize,
    lineHeight,
    theme,
    isLoaded,
    timestamp: new Date().toISOString()
  });

  // Don't render until settings are loaded to prevent flash
  if (!isLoaded) {
    return (
      <div className="my-8 animate-pulse">
        <div className="h-8 bg-zinc-800 rounded mb-4" />
        <div className="space-y-3">
          <div className="h-4 bg-zinc-800 rounded" />
          <div className="h-4 bg-zinc-800 rounded w-5/6" />
          <div className="h-4 bg-zinc-800 rounded w-4/6" />
        </div>
      </div>
    );
  }

  // Show loading state if current font is not loaded yet
  if (!currentFontLoaded && !allFontsLoaded) {
    return (
      <div className="my-8">
        <div className="flex items-center justify-center p-4 text-muted-foreground">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-3"></div>
          Loading font: {currentFontName}...
        </div>
      </div>
    );
  }

  return (
    <div className="my-4 sm:my-8">
      <ChapterContent
        // Remove key prop to prevent unnecessary re-renders
        chapter={chapter}
        fontSize={fontSize}
        lineHeight={lineHeight}
        theme={theme}
        fontFamily={fontFamily}
      />

      <ChapterStats chapter={chapter} />
    </div>
  );
};

export default ReaderView;