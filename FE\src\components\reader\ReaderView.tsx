'use client';

import React from 'react';
import { Chapter } from '@/types/story';
import ChapterContent from './ChapterContent';
import ChapterStats from './ChapterStats';
import { useReaderSettings } from '@/hooks/useReaderSettings';

interface ReaderViewProps {
  chapter: Chapter;
}

const ReaderView: React.FC<ReaderViewProps> = ({ chapter }) => {
  const { fontSize, lineHeight, theme, fontFamily, isLoaded } = useReaderSettings();

  // Don't render until settings are loaded to prevent flash
  if (!isLoaded) {
    return (
      <div className="my-8 animate-pulse">
        <div className="h-8 bg-zinc-800 rounded mb-4" />
        <div className="space-y-3">
          <div className="h-4 bg-zinc-800 rounded" />
          <div className="h-4 bg-zinc-800 rounded w-5/6" />
          <div className="h-4 bg-zinc-800 rounded w-4/6" />
        </div>
      </div>
    );
  }

  return (
    <div className="my-4 sm:my-8">
      <ChapterContent
        key={`${fontFamily}-${theme}`}
        chapter={chapter}
        fontSize={fontSize}
        lineHeight={lineHeight}
        theme={theme}
        fontFamily={fontFamily}
      />
      
      <ChapterStats chapter={chapter} />
    </div>
  );
};

export default ReaderView;