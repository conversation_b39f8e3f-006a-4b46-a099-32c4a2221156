'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, CheckCircle, Clock, Zap, Eye, Cpu, HardDrive } from 'lucide-react';

// ============================================================================
// Performance Monitoring Component for Task 5.0
// ============================================================================

interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  domContentLoaded?: number;
  loadComplete?: number;
  memoryUsage?: {
    used: number;
    total: number;
    percentage: number;
  };
  
  // Lighthouse scores (simulated)
  performance?: number;
  accessibility?: number;
  bestPractices?: number;
  seo?: number;
}

interface PerformanceIssue {
  type: 'error' | 'warning' | 'info';
  category: 'performance' | 'accessibility' | 'seo' | 'best-practices';
  message: string;
  impact: 'high' | 'medium' | 'low';
  suggestion?: string;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [issues, setIssues] = useState<PerformanceIssue[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Collect Core Web Vitals
  const collectWebVitals = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Performance Observer for Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1] as any;
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
        }
      });
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP observation not supported');
      }

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
        });
      });
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.warn('FID observation not supported');
      }

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        setMetrics(prev => ({ ...prev, cls: clsValue }));
      });
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.warn('CLS observation not supported');
      }
    }

    // Navigation Timing API
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      setMetrics(prev => ({
        ...prev,
        fcp: navigation?.responseStart - navigation?.fetchStart || 0,
        ttfb: timing.responseStart - timing.navigationStart,
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart
      }));
    }

    // Memory usage (if available)
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      setMetrics(prev => ({
        ...prev,
        memoryUsage: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
        }
      }));
    }
  }, []);

  // Simulate Lighthouse scores based on metrics
  const calculateLighthouseScores = useCallback((metrics: PerformanceMetrics) => {
    const scores = {
      performance: 100,
      accessibility: 95,
      bestPractices: 92,
      seo: 88
    };

    // Adjust performance score based on metrics
    if (metrics.lcp && metrics.lcp > 2500) scores.performance -= 20;
    if (metrics.fid && metrics.fid > 100) scores.performance -= 15;
    if (metrics.cls && metrics.cls > 0.1) scores.performance -= 10;
    if (metrics.ttfb && metrics.ttfb > 600) scores.performance -= 10;

    return scores;
  }, []);

  // Analyze performance and generate issues
  const analyzePerformance = useCallback((metrics: PerformanceMetrics) => {
    const newIssues: PerformanceIssue[] = [];

    // LCP analysis
    if (metrics.lcp) {
      if (metrics.lcp > 4000) {
        newIssues.push({
          type: 'error',
          category: 'performance',
          message: `Largest Contentful Paint is ${Math.round(metrics.lcp)}ms (Poor)`,
          impact: 'high',
          suggestion: 'Optimize images, reduce server response times, and eliminate render-blocking resources'
        });
      } else if (metrics.lcp > 2500) {
        newIssues.push({
          type: 'warning',
          category: 'performance',
          message: `Largest Contentful Paint is ${Math.round(metrics.lcp)}ms (Needs Improvement)`,
          impact: 'medium',
          suggestion: 'Consider image optimization and lazy loading'
        });
      }
    }

    // FID analysis
    if (metrics.fid) {
      if (metrics.fid > 300) {
        newIssues.push({
          type: 'error',
          category: 'performance',
          message: `First Input Delay is ${Math.round(metrics.fid)}ms (Poor)`,
          impact: 'high',
          suggestion: 'Reduce JavaScript execution time and optimize event handlers'
        });
      } else if (metrics.fid > 100) {
        newIssues.push({
          type: 'warning',
          category: 'performance',
          message: `First Input Delay is ${Math.round(metrics.fid)}ms (Needs Improvement)`,
          impact: 'medium',
          suggestion: 'Consider code splitting and reducing main thread work'
        });
      }
    }

    // CLS analysis
    if (metrics.cls) {
      if (metrics.cls > 0.25) {
        newIssues.push({
          type: 'error',
          category: 'performance',
          message: `Cumulative Layout Shift is ${metrics.cls.toFixed(3)} (Poor)`,
          impact: 'high',
          suggestion: 'Set explicit dimensions for images and ads, avoid inserting content above existing content'
        });
      } else if (metrics.cls > 0.1) {
        newIssues.push({
          type: 'warning',
          category: 'performance',
          message: `Cumulative Layout Shift is ${metrics.cls.toFixed(3)} (Needs Improvement)`,
          impact: 'medium',
          suggestion: 'Reserve space for dynamic content and use CSS transforms for animations'
        });
      }
    }

    // Memory usage analysis
    if (metrics.memoryUsage && metrics.memoryUsage.percentage > 80) {
      newIssues.push({
        type: 'warning',
        category: 'performance',
        message: `High memory usage: ${metrics.memoryUsage.percentage}%`,
        impact: 'medium',
        suggestion: 'Consider implementing virtual scrolling and cleaning up unused objects'
      });
    }

    setIssues(newIssues);
  }, []);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    collectWebVitals();
    
    const interval = setInterval(() => {
      collectWebVitals();
      setLastUpdate(new Date());
    }, 5000);

    return () => {
      clearInterval(interval);
      setIsMonitoring(false);
    };
  }, [collectWebVitals]);

  // Update lighthouse scores and analyze when metrics change
  useEffect(() => {
    const scores = calculateLighthouseScores(metrics);
    setMetrics(prev => ({ ...prev, ...scores }));
    analyzePerformance(metrics);
  }, [metrics.lcp, metrics.fid, metrics.cls, metrics.ttfb, calculateLighthouseScores, analyzePerformance]);

  // Auto-start monitoring on mount
  useEffect(() => {
    const cleanup = startMonitoring();
    return cleanup;
  }, [startMonitoring]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number): 'default' | 'secondary' | 'destructive' => {
    if (score >= 90) return 'default';
    if (score >= 70) return 'secondary';
    return 'destructive';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Monitor</h2>
          <p className="text-muted-foreground">
            Real-time performance metrics and optimization suggestions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isMonitoring ? 'default' : 'secondary'}>
            {isMonitoring ? 'Monitoring' : 'Stopped'}
          </Badge>
          {lastUpdate && (
            <span className="text-sm text-muted-foreground">
              Last update: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="vitals">Core Web Vitals</TabsTrigger>
          <TabsTrigger value="lighthouse">Lighthouse Scores</TabsTrigger>
          <TabsTrigger value="issues">Issues</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Performance Score</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.performance || 0}</div>
                <Progress value={metrics.performance || 0} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Accessibility</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.accessibility || 0}</div>
                <Progress value={metrics.accessibility || 0} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.memoryUsage?.percentage || 0}%
                </div>
                <Progress value={metrics.memoryUsage?.percentage || 0} className="mt-2" />
                {metrics.memoryUsage && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatBytes(metrics.memoryUsage.used)} / {formatBytes(metrics.memoryUsage.total)}
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Issues Found</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{issues.length}</div>
                <div className="flex gap-1 mt-2">
                  <Badge variant="destructive" className="text-xs">
                    {issues.filter(i => i.type === 'error').length} Errors
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {issues.filter(i => i.type === 'warning').length} Warnings
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Largest Contentful Paint (LCP)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.lcp ? formatTime(metrics.lcp) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Good: &lt; 2.5s, Poor: &gt; 4.0s
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">First Input Delay (FID)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.fid ? formatTime(metrics.fid) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Good: &lt; 100ms, Poor: &gt; 300ms
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Cumulative Layout Shift (CLS)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.cls ? metrics.cls.toFixed(3) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Good: &lt; 0.1, Poor: &gt; 0.25
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">First Contentful Paint (FCP)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.fcp ? formatTime(metrics.fcp) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Good: &lt; 1.8s, Poor: &gt; 3.0s
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Time to First Byte (TTFB)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.ttfb ? formatTime(metrics.ttfb) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Good: &lt; 600ms, Poor: &gt; 1.5s
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Load Complete</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.loadComplete ? formatTime(metrics.loadComplete) : 'Measuring...'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Total page load time
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="lighthouse" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { key: 'performance', label: 'Performance', icon: Zap },
              { key: 'accessibility', label: 'Accessibility', icon: Eye },
              { key: 'bestPractices', label: 'Best Practices', icon: CheckCircle },
              { key: 'seo', label: 'SEO', icon: HardDrive }
            ].map(({ key, label, icon: Icon }) => {
              const score = metrics[key as keyof PerformanceMetrics] as number || 0;
              return (
                <Card key={key}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{label}</CardTitle>
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className={`text-2xl font-bold ${getScoreColor(score)}`}>
                      {score}
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Progress value={score} className="flex-1" />
                      <Badge variant={getScoreBadgeVariant(score)}>
                        {score >= 90 ? 'Good' : score >= 70 ? 'Needs Improvement' : 'Poor'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="issues" className="space-y-4">
          {issues.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold">No Issues Found</h3>
                  <p className="text-muted-foreground">
                    Your application is performing well!
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {issues.map((issue, index) => (
                <Card key={index}>
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {issue.type === 'error' ? (
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        ) : (
                          <Clock className="h-5 w-5 text-yellow-500" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant={issue.type === 'error' ? 'destructive' : 'secondary'}>
                            {issue.category}
                          </Badge>
                          <Badge variant="outline">
                            {issue.impact} impact
                          </Badge>
                        </div>
                        <h4 className="font-medium">{issue.message}</h4>
                        {issue.suggestion && (
                          <p className="text-sm text-muted-foreground mt-1">
                            [TIP] {issue.suggestion}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceMonitor;