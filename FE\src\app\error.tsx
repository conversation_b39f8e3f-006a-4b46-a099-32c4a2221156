/**
 * Next.js Error Page for Route-Level Error Handling
 * Handles errors that occur during page rendering or data fetching
 */

'use client';

import { useEffect } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { handleError, createAppError } from '@/utils/errorHandling';
import { AppErrorType } from '@/types/errors';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // Log the error when the component mounts
    const appError = createAppError(
      error.message,
      AppErrorType.CLIENT,
      true,
      'An error occurred while loading this page',
      {
        digest: error.digest,
        stack: error.stack,
        name: error.name
      }
    );

    handleError(appError, {
      showToast: false, // Don't show toast on error pages
      logError: true,
      context: {
        page: 'error-page',
        digest: error.digest
      }
    });
  }, [error]);

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleCopyError = () => {
    const errorDetails = {
      message: error.message,
      name: error.name,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      stack: error.stack
    };
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
  };

  const isNetworkError = error.message.toLowerCase().includes('network') || 
                        error.message.toLowerCase().includes('fetch');
  const isServerError = error.message.toLowerCase().includes('server') ||
                       error.message.toLowerCase().includes('500');

  const getErrorTitle = () => {
    if (isNetworkError) return 'Connection Error';
    if (isServerError) return 'Server Error';
    return 'Something went wrong';
  };

  const getErrorDescription = () => {
    if (isNetworkError) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    if (isServerError) {
      return 'The server encountered an error while processing your request. Please try again later.';
    }
    return 'An unexpected error occurred while loading this page.';
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-semibold text-red-900 dark:text-red-100">
            {getErrorTitle()}
          </CardTitle>
          <CardDescription className="text-red-700 dark:text-red-300">
            {getErrorDescription()}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <Bug className="h-4 w-4" />
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription className="mt-2 font-mono text-sm break-words">
              {error.message}
              {error.digest && (
                <div className="mt-1 text-xs opacity-70">
                  Error ID: {error.digest}
                </div>
              )}
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              onClick={reset}
              variant="default"
              className="flex-1"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button
              onClick={handleReload}
              variant="outline"
              className="flex-1"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Reload Page
            </Button>
          </div>
          
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              onClick={handleGoHome}
              variant="outline"
              className="flex-1"
            >
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
            <Button
              onClick={handleCopyError}
              variant="ghost"
              size="sm"
              className="flex-1"
            >
              <Bug className="mr-2 h-4 w-4" />
              Copy Error
            </Button>
          </div>
          
          {process.env.NODE_ENV === 'development' && error.stack && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                Stack Trace (Development)
              </summary>
              <pre className="mt-2 whitespace-pre-wrap text-xs bg-muted p-2 rounded border overflow-auto max-h-40">
                {error.stack}
              </pre>
            </details>
          )}
          
          <div className="text-center text-xs text-muted-foreground">
            If this problem persists, please contact support.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}