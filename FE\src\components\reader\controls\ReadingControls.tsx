"use client"

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import FontSizeControl from './FontSizeControl';
import LineHeightControl from './LineHeightControl';
import ThemeToggle from './ThemeToggle';
import FontFamilyControl from './FontFamilyControl';

type FontFamily = 'inter' | 'georgia' | 'times' | 'merriweather' | 'source-serif' | 'noto-serif' | 'roboto' | 'jetbrains-mono';

interface ReadingControlsProps {
  fontSize: number;
  lineHeight: number;
  fontFamily: FontFamily;
  isDarkMode: boolean;
  onFontSizeIncrease: () => void;
  onFontSizeDecrease: () => void;
  onLineHeightChange: (value: number) => void;
  onFontFamilyChange: (fontFamily: FontFamily) => void;
  onThemeToggle: (isDark: boolean) => void;
}

const ReadingControls: React.FC<ReadingControlsProps> = ({
  fontSize,
  lineHeight,
  fontFamily,
  isDarkMode,
  onFontSizeIncrease,
  onFontSizeDecrease,
  onLineHeightChange,
  onFontFamilyChange,
  onThemeToggle
}) => {
  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-wrap items-center gap-6">
          <FontSizeControl
            fontSize={fontSize}
            onIncrease={onFontSizeIncrease}
            onDecrease={onFontSizeDecrease}
          />
          
          <LineHeightControl
            lineHeight={lineHeight}
            onChange={onLineHeightChange}
          />
          
          <FontFamilyControl
            fontFamily={fontFamily}
            onFontFamilyChange={onFontFamilyChange}
          />
          
          <ThemeToggle
            isDarkMode={isDarkMode}
            onToggle={onThemeToggle}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ReadingControls;