// A simple in-memory cache
const cache = new Map<string, { data: any; timestamp: number }>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const getCache = (key: string) => {
  const cached = cache.get(key);
  if (!cached) return null;

  const isExpired = (Date.now() - cached.timestamp) > CACHE_DURATION;
  if (isExpired) {
    cache.delete(key);
    return null;
  }

  return cached.data;
};

export const setCache = (key: string, data: any) => {
  cache.set(key, { data, timestamp: Date.now() });
};