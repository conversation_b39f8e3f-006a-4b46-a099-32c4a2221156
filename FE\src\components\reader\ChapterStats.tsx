"use client"

import React, { memo, useMemo } from 'react';
import { Chapter } from '@/types/story';
import { Calendar, FileText } from 'lucide-react';
import { ChapterStatusBadges } from '@/components/ui/StatusBadge';

interface ChapterStatsProps {
  chapter: Chapter;
}

const ChapterStats: React.FC<ChapterStatsProps> = memo(({ chapter }) => {
  const formattedDate = useMemo(() => {
    return new Date(chapter.updated_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, [chapter.updated_at]);

  const formattedWordCount = useMemo(() => {
    const count = chapter.word_count || 0;
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  }, [chapter.word_count]);

  return (
    <div className="mt-6 text-center space-y-3">
      {/* Chapter Info */}
      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
        <FileText className="h-4 w-4" />
        <span>Chapter {chapter.chapter_number}</span>
        <span>·</span>
        <span>{formattedWordCount} words</span>
      </div>

      {/* Updated Date */}
      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
        <Calendar className="h-4 w-4" />
        <span>Updated: {formattedDate}</span>
      </div>

      {/* Status Badges */}
      <ChapterStatusBadges
        isScraped={chapter.is_scraped}
        isEnhanced={chapter.is_enhanced}
        className="justify-center"
        size="md"
      />
    </div>
  );
});

ChapterStats.displayName = 'ChapterStats';

export default ChapterStats;