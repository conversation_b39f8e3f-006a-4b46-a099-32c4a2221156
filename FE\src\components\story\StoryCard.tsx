'use client';

import React, { memo } from 'react';
import { Story } from '@/types/story';
import { CompoundCard } from '@/components/ui/CompoundCard';
import { StoryStatusBadge } from '@/components/ui/StatusBadge';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Calendar, User, Tag } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StoryCardProps {
  story: Story;
}

const StoryCard = memo(({ story }: StoryCardProps) => {
  const chapterText = story.total_chapters 
    ? `${story.total_chapters_scraped}/${story.total_chapters}`
    : '0';
  
  const completionPercentage = story.total_chapters 
    ? Math.round((story.total_chapters_scraped / story.total_chapters) * 100)
    : 0;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1d';
    if (diffDays < 7) return `${diffDays}d`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)}w`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <CompoundCard.Root
      href={`/stories/${story.id}`}
      className="group hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:scale-[1.03] active:scale-[0.98] border border-border/40 bg-card rounded-lg overflow-hidden hover:border-primary/30 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      aria-label={`Read ${story.title} by ${story.author || 'Unknown author'}. ${story.total_chapters ? `${completionPercentage}% complete with ${chapterText} chapters` : 'Chapter count unknown'}.`}
    >
      <div className="relative h-32 overflow-hidden rounded-t-lg">
        <img
          src={story.cover_image_url || '/placeholder-cover.svg'}
          alt={`Cover image for ${story.title}`}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-cover.svg';
            target.alt = `Placeholder cover for ${story.title}`;
          }}
        />
        
        {/* Overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
          <div className="absolute top-1.5 left-1.5 right-1.5 flex justify-between items-start">
            <StoryStatusBadge
              status={story.status}
              size="sm"
              showIcon={false}
            />
            {story.total_chapters && (
              <Badge 
                variant="secondary" 
                className="bg-black/70 text-white border-0 text-xs px-1.5 py-0.5 h-auto font-medium transition-all duration-200 group-hover:bg-primary group-hover:scale-105"
              >
                {completionPercentage}%
              </Badge>
            )}
          </div>
          
          {/* Progress bar */}
          {story.total_chapters && (
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white/20">
              <div 
                className="h-full bg-primary transition-all duration-500 group-hover:bg-primary/80 relative overflow-hidden"
                style={{ width: `${completionPercentage}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
              </div>
            </div>
          )}
        </div>
      </div>
      
      <CompoundCard.Content className="p-3 space-y-2">
        <CompoundCard.Title className="line-clamp-2 group-hover:text-primary transition-colors font-medium text-sm leading-tight mb-1">
          {story.title}
        </CompoundCard.Title>
        
        {/* Compact author and metadata */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1 min-w-0 flex-1">
            <User className="h-3 w-3 flex-shrink-0" />
            <span className="truncate">{story.author || 'Unknown'}</span>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0 ml-2">
            <BookOpen className="h-3 w-3" />
            <span>{chapterText}</span>
          </div>
        </div>
        
        {/* Compact genres */}
        {story.genres && story.genres.length > 0 && (
          <div className="flex gap-1 flex-wrap">
            {story.genres.slice(0, 3).map((genre, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="text-xs px-1.5 py-0 h-4 border-muted-foreground/20 text-muted-foreground/80 hover:bg-primary/10 hover:border-primary/40 hover:text-primary transition-all duration-200 hover:scale-105 cursor-pointer"
              >
                {genre}
              </Badge>
            ))}
            {story.genres.length > 3 && (
              <Badge 
                variant="outline" 
                className="text-xs px-1.5 py-0 h-4 border-muted-foreground/20 text-muted-foreground/60"
              >
                +{story.genres.length - 3}
              </Badge>
            )}
          </div>
        )}
        
        {/* Compact footer */}
        <div className="flex items-center justify-between text-xs text-muted-foreground/70 pt-1">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(story.updated_at)}</span>
          </div>
          {story.total_chapters && (
            <span className="text-primary/70 font-medium">
              {Math.round((story.total_chapters_scraped / story.total_chapters) * 100)}% done
            </span>
          )}
        </div>
      </CompoundCard.Content>
    </CompoundCard.Root>
  );
});

StoryCard.displayName = 'StoryCard';

export default StoryCard;