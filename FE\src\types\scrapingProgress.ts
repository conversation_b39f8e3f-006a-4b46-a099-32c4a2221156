/**
 * TypeScript interfaces for enhanced scraping progress tracking
 * Supports real-time progress updates, phase tracking, and cancellation
 */

// Enum for scraping phases
export enum ScrapingPhase {
  EXTRACTING_STORY_INFO = 'extracting_story_info',
  EXTRACTING_CHAPTERS = 'extracting_chapters', 
  SCRAPING_CONTENT = 'scraping_content',
  STORING_DATA = 'storing_data',
  COMPLETED = 'completed',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

// Progress data interface
export interface ProgressData {
  // Chapter tracking
  chapters_total?: number;
  chapters_completed?: number;
  chapters_successful?: number;
  current_chapter?: string;
  
  // Page tracking
  pages_total?: number;
  pages_completed?: number;
  current_page?: number;
  
  // Phase tracking
  current_phase?: string;
  
  // Error tracking
  errors_count?: number;
  error_details?: string;
  
  // Additional context data
  [key: string]: any;
}

// Main progress message interface
export interface ScrapingProgressMessage {
  type: 'progress' | 'error' | 'completion' | 'cancellation';
  job_id: string;
  phase: ScrapingPhase;
  current_step: number;
  total_steps: number;
  message: string;
  data: ProgressData;
  timestamp: string;
}

// WebSocket message wrapper
export interface WebSocketMessage {
  type: string;
  payload: ScrapingProgressMessage | any;
}

// Progress state for UI components
export interface ProgressState {
  isActive: boolean;
  isMinimized: boolean;
  canCancel: boolean;
  phase: ScrapingPhase;
  currentStep: number;
  totalSteps: number;
  message: string;
  data: ProgressData;
  logs: string[];
  startTime?: Date;
  estimatedTimeRemaining?: number;
  error?: string;
}

// Time estimation data
export interface TimeEstimation {
  startTime: Date;
  currentTime: Date;
  progress: number; // 0-1
  estimatedTimeRemaining: number; // in milliseconds
  estimatedCompletionTime: Date;
  averageTimePerStep: number;
}

// Cancellation request/response
export interface CancellationRequest {
  reason?: string;
}

export interface CancellationResponse {
  success: boolean;
  message: string;
  data?: {
    job_id: string;
    cancelled_at: string;
  };
}

// Progress summary for completion
export interface ProgressSummary {
  job_id: string;
  total_duration: number;
  chapters_processed: number;
  chapters_successful: number;
  pages_processed: number;
  errors_encountered: number;
  completion_time: string;
}

// Modal/Toast state management
export interface ModalState {
  isOpen: boolean;
  isMinimized: boolean;
  canMinimize: boolean;
  canCancel: boolean;
  showLogs: boolean;
}

// Hook return types
export interface UseScrapingJobReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // Progress state
  progress: ProgressState;
  
  // Actions
  connect: (jobId: string) => void;
  disconnect: () => void;
  cancelJob: (reason?: string) => Promise<void>;
  
  // Utilities
  clearLogs: () => void;
  addLog: (message: string) => void;
}

export interface UseProgressModalReturn {
  // Modal state
  modalState: ModalState;
  
  // Actions
  openModal: () => void;
  closeModal: () => void;
  minimizeModal: () => void;
  maximizeModal: () => void;
  toggleLogs: () => void;
  
  // Configuration
  setCanMinimize: (canMinimize: boolean) => void;
  setCanCancel: (canCancel: boolean) => void;
}

// Component props interfaces
export interface ProgressBarProps {
  progress: number; // 0-100
  phase: ScrapingPhase;
  animated?: boolean;
  showPercentage?: boolean;
  className?: string;
}

export interface PhaseIndicatorProps {
  phase: ScrapingPhase;
  message: string;
  className?: string;
}

export interface TimeEstimatorProps {
  startTime: Date;
  progress: number;
  className?: string;
}

export interface ScrapingProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
  jobId: string;
  canCancel?: boolean;
  showLogs?: boolean;
}

export interface ScrapingProgressToastProps {
  isVisible: boolean;
  onMaximize: () => void;
  onCancel?: () => void;
  jobId: string;
  progress: ProgressState;
}

// Utility types
export type ProgressCallback = (progress: ScrapingProgressMessage) => void;
export type ErrorCallback = (error: string) => void;
export type CompletionCallback = (summary: ProgressSummary) => void;
export type CancellationCallback = () => void;

// Constants
export const PROGRESS_UPDATE_INTERVAL = 500; // ms
export const RECONNECT_INTERVAL = 3000; // ms
export const MAX_RECONNECT_ATTEMPTS = 5;
export const MAX_LOG_ENTRIES = 100;

// Phase display configuration
export const PHASE_CONFIG: Record<ScrapingPhase, { label: string; color: string; icon: string }> = {
  [ScrapingPhase.EXTRACTING_STORY_INFO]: {
    label: 'Extracting Story Info',
    color: 'blue',
    icon: '[READING]'
  },
  [ScrapingPhase.EXTRACTING_CHAPTERS]: {
    label: 'Extracting Chapters',
    color: 'purple', 
    icon: '[PROCESSING]'
  },
  [ScrapingPhase.SCRAPING_CONTENT]: {
    label: 'Scraping Content',
    color: 'orange',
    icon: '[FAST]'
  },
  [ScrapingPhase.STORING_DATA]: {
    label: 'Storing Data',
    color: 'green',
    icon: '[SAVING]'
  },
  [ScrapingPhase.COMPLETED]: {
    label: 'Completed',
    color: 'green',
    icon: '[SUCCESS]'
  },
  [ScrapingPhase.ERROR]: {
    label: 'Error',
    color: 'red',
    icon: '[ERROR]'
  },
  [ScrapingPhase.CANCELLED]: {
    label: 'Cancelled',
    color: 'gray',
    icon: '[STOPPED]'
  }
};