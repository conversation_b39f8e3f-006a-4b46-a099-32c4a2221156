'use client';

import { useState } from 'react';
import { Chapter, Story } from '@/types/story';
import ReaderView from './ReaderView';
import ReaderLayout from './ReaderLayout';
import ReaderSettings from './ReaderSettings';

interface ChapterReaderWrapperProps {
  story: Story;
  chapter: Chapter;
  storyId: string;
}

const ChapterReaderWrapper = ({ story, chapter, storyId }: ChapterReaderWrapperProps) => {
  const [showSettings, setShowSettings] = useState(false);

  const handleSettingsOpen = () => {
    setShowSettings(true);
  };

  const handleSettingsClose = () => {
    setShowSettings(false);
  };

  return (
    <ReaderLayout
      story={story}
      chapter={chapter}
      storyId={storyId}
      onSettingsOpen={handleSettingsOpen}
    >
      {/* Minimal Content Container */}
      <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
        {/* Chapter Content - Prioritized for mobile reading */}
        <ReaderView chapter={chapter} />
      </div>

      {/* Reader Settings Modal/Bottom Sheet */}
      <ReaderSettings
        isOpen={showSettings}
        onClose={handleSettingsClose}
      />
    </ReaderLayout>
  );
};

export default ChapterReaderWrapper;