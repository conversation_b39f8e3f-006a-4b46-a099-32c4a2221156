'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useReaderSettings, FONT_FAMILIES } from '@/hooks/useReaderSettings';
import { Settings, Type, LineChart, Palette, Sun, Moon, FileText, BookOpen } from 'lucide-react';

interface ReaderSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const ReaderSettings: React.FC<ReaderSettingsProps> = ({ isOpen, onClose }) => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const {
    fontSize,
    lineHeight,
    theme,
    fontFamily,
    setFontSize,
    setLineHeight,
    setTheme,
    setFontFamily,
    resetSettings
  } = useReaderSettings();

  const themes = [
    { id: 'dark', name: 'Dark', icon: Moon, bg: 'bg-zinc-900', text: 'text-white' },
    { id: 'light', name: 'Light', icon: Sun, bg: 'bg-white', text: 'text-black' },
    { id: 'sepia', name: 'Sepia', icon: FileText, bg: 'bg-amber-50', text: 'text-amber-900' }
  ] as const;

  const SettingsContent = () => (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Font Size Control */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Type className="h-4 w-4 text-muted-foreground" />
          <label className="text-sm font-medium">Font Size</label>
          <span className="ml-auto text-sm text-muted-foreground">{fontSize}px</span>
        </div>
        <Slider
          value={[fontSize]}
          onValueChange={(value) => setFontSize(value[0])}
          min={12}
          max={28}
          step={2}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Small</span>
          <span>Large</span>
        </div>
      </div>

      {/* Line Height Control */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <LineChart className="h-4 w-4 text-muted-foreground" />
          <label className="text-sm font-medium">Line Height</label>
          <span className="ml-auto text-sm text-muted-foreground">{lineHeight.toFixed(1)}</span>
        </div>
        <Slider
          value={[lineHeight]}
          onValueChange={(value) => setLineHeight(value[0])}
          min={1.2}
          max={2.5}
          step={0.1}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Tight</span>
          <span>Loose</span>
        </div>
      </div>

      {/* Font Family Selection */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <BookOpen className="h-4 w-4 text-muted-foreground" />
          <label className="text-sm font-medium">Font Family</label>
        </div>
        <Select value={fontFamily} onValueChange={setFontFamily}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select font family" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(FONT_FAMILIES).map(([key, font]) => (
              <SelectItem key={key} value={key}>
                <div className="flex flex-col gap-1">
                  <span 
                    className="font-medium"
                    style={{ fontFamily: font.cssFamily }}
                  >
                    {font.displayName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {font.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Theme Selection */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4 text-muted-foreground" />
          <label className="text-sm font-medium">Reading Theme</label>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {themes.map((themeOption) => {
            const Icon = themeOption.icon;
            return (
              <button
                key={themeOption.id}
                onClick={() => setTheme(themeOption.id)}
                className={`relative flex flex-col items-center gap-2 p-3 rounded-lg border-2 transition-all touch-manipulation ${
                  theme === themeOption.id
                    ? 'border-primary bg-primary/10'
                    : 'border-border hover:border-primary/50'
                }`}
              >
                <div className={`w-8 h-6 rounded ${themeOption.bg} border border-border flex items-center justify-center`}>
                  <Icon className={`h-3 w-3 ${themeOption.text}`} />
                </div>
                <span className="text-xs font-medium">{themeOption.name}</span>
                {theme === themeOption.id && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full" />
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Preview Text */}
      <div className="space-y-3">
        <label className="text-sm font-medium">Preview</label>
        <div 
          className={`p-4 rounded-lg border transition-all ${
            theme === 'light' ? 'bg-white text-black border-gray-200' :
            theme === 'sepia' ? 'bg-amber-50 text-amber-900 border-amber-200' :
            'bg-zinc-900 text-white border-zinc-700'
          }`}
          style={{
            fontSize: `${fontSize}px`,
            lineHeight: lineHeight,
            fontFamily: FONT_FAMILIES[fontFamily].cssFamily
          }}
        >
          <p className="text-justify">
            Đây là bản xem trước cách trải nghiệm đọc của bạn sẽ trông như thế nào với các cài đặt hiện tại. 
            Hãy điều chỉnh kích thước font, độ cao dòng, font chữ và chủ đề theo sở thích của bạn. 
            Những font chữ này được tối ưu hóa đặc biệt cho việc đọc dài và giảm mỏi mắt.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-2 pt-4 border-t">
        <Button
          variant="outline"
          onClick={resetSettings}
          className="flex-1 touch-manipulation min-h-touch-target sm:min-h-0"
        >
          Reset to Default
        </Button>
        <Button
          onClick={onClose}
          className="flex-1 touch-manipulation min-h-touch-target sm:min-h-0"
        >
          Apply Settings
        </Button>
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Reader Settings
            </DialogTitle>
          </DialogHeader>
          <SettingsContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <DrawerTitle className="flex items-center gap-2 justify-center">
            <Settings className="h-5 w-5" />
            Reader Settings
          </DrawerTitle>
        </DrawerHeader>
        <div className="overflow-y-auto">
          <SettingsContent />
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default ReaderSettings;