@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 72% 51%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 45 93% 47%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 72% 51%;
    --reading-bg: 45 29% 97%;
    --reading-text: 0 0% 13%;
    --chapter-highlight: 45 93% 47%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 72% 51%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 45 93% 47%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 72% 51%;
    --reading-bg: 0 0% 7%;
    --reading-text: 0 0% 90%;
    --chapter-highlight: 45 93% 47%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  /* High Contrast Mode for Accessibility */
  .high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 80%;
    --accent: 60 100% 50%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    --success: 120 100% 50%;
    --success-foreground: 0 0% 0%;
    --warning: 60 100% 50%;
    --warning-foreground: 0 0% 0%;
    --border: 0 0% 40%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
    --reading-bg: 0 0% 0%;
    --reading-text: 0 0% 100%;
    --chapter-highlight: 60 100% 50%;
  }

  /* Sepia theme for reading */
  .sepia {
    --background: 45 29% 97%;
    --foreground: 25 25% 15%;
    --card: 45 29% 97%;
    --card-foreground: 25 25% 15%;
    --popover: 45 29% 97%;
    --popover-foreground: 25 25% 15%;
    --primary: 25 62% 35%;
    --primary-foreground: 45 29% 97%;
    --secondary: 40 20% 90%;
    --secondary-foreground: 25 25% 15%;
    --muted: 40 15% 92%;
    --muted-foreground: 25 15% 45%;
    --accent: 35 80% 45%;
    --accent-foreground: 45 29% 97%;
    --destructive: 0 70% 45%;
    --destructive-foreground: 45 29% 97%;
    --success: 120 50% 35%;
    --success-foreground: 45 29% 97%;
    --warning: 35 80% 40%;
    --warning-foreground: 45 29% 97%;
    --border: 40 15% 85%;
    --input: 40 15% 85%;
    --ring: 25 62% 35%;
    --reading-bg: 45 29% 97%;
    --reading-text: 25 25% 15%;
    --chapter-highlight: 35 80% 45%;
  }
}

/* Shimmer animation for skeleton loading */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Fade-in animation for story cards */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    /* Enable smooth scrolling */
    scroll-behavior: smooth;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
    /* Improve text rendering */
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
  
  body {
    @apply bg-background text-foreground;
    /* Mobile-first font sizing */
    font-size: 16px;
    line-height: 1.6;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
    /* Prevent text selection on UI elements */
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection in content areas */
  p, span, div[class*="content"], div[class*="description"], div[class*="text"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
  }
  
  /* Touch-friendly button and link styling */
  button, a, [role="button"] {
    /* Minimum touch target size */
    min-height: 44px;
    min-width: 44px;
    /* Remove tap highlight on mobile */
    -webkit-tap-highlight-color: transparent;
    /* Improve touch responsiveness */
    touch-action: manipulation;
  }
  
  /* Enhanced focus styles for accessibility */
  button:focus-visible, 
  a:focus-visible, 
  [role="button"]:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px hsl(var(--ring) / 0.2);
  }

  /* High contrast focus styles */
  .high-contrast button:focus-visible,
  .high-contrast a:focus-visible,
  .high-contrast [role="button"]:focus-visible,
  .high-contrast input:focus-visible,
  .high-contrast select:focus-visible,
  .high-contrast textarea:focus-visible {
    outline: 3px solid hsl(var(--ring));
    outline-offset: 2px;
    box-shadow: 0 0 0 6px hsl(var(--ring) / 0.3);
  }
  
  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
  
  /* Thin scrollbar variant */
  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
  }
  
  /* Accessibility improvements */
  
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* High contrast mode enhancements */
  .high-contrast {
    /* Ensure all borders are visible */
    * {
      border-color: hsl(var(--border)) !important;
    }
    
    /* Enhanced button visibility */
    button, [role="button"] {
      border: 2px solid hsl(var(--foreground)) !important;
    }
    
    /* Enhanced link visibility */
    a {
      text-decoration: underline !important;
      text-decoration-thickness: 2px !important;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Mobile-specific improvements */
  @media (max-width: 768px) {
    /* Improve readability on small screens */
    body {
      font-size: 14px;
      line-height: 1.5;
    }
    
    /* Larger touch targets on mobile */
    button, a, [role="button"], input, select {
      min-height: 44px;
      min-width: 44px;
    }
    
    /* Hide scrollbars on mobile for cleaner look */
    .mobile-hide-scrollbar {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    
    .mobile-hide-scrollbar::-webkit-scrollbar {
      display: none;
    }
  }
  
  /* Safe area support for devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
  
  .safe-area-padding-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-padding-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  /* Touch target utilities */
  .min-h-touch-target {
    min-height: 44px;
  }
  
  .min-w-touch-target {
    min-width: 44px;
  }
  
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Loading states with accessibility */
  .loading-skeleton {
    background: linear-gradient(90deg, 
      hsl(var(--muted)) 25%, 
      hsl(var(--muted) / 0.5) 50%, 
      hsl(var(--muted)) 75%
    );
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
  }
  
  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  /* Respect reduced motion for loading animations */
  @media (prefers-reduced-motion: reduce) {
    .loading-skeleton {
      animation: none;
      background: hsl(var(--muted));
    }
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Consistent interactive elements */
  .interactive-element {
    @apply transition-all duration-200 hover:scale-105 active:scale-95;
  }
  
  .interactive-button {
    @apply transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
  
  .interactive-card {
    @apply transition-all duration-300 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
  
  /* Consistent typography scale */
  .text-display {
    @apply text-3xl font-bold tracking-tight;
  }
  
  .text-heading {
    @apply text-xl font-semibold;
  }
  
  .text-subheading {
    @apply text-lg font-medium;
  }
  
  .text-body {
    @apply text-base leading-relaxed;
  }
  
  .text-caption {
    @apply text-sm text-muted-foreground;
  }
  
  /* Consistent spacing utilities */
  .section-spacing {
    @apply py-8 px-4;
  }
  
  .card-spacing {
    @apply p-6;
  }
  
  .element-spacing {
    @apply space-y-4;
  }
}