/**
 * EnhancedToast Component
 * Custom toast component with refresh button integration
 * Built on top of Sonner toast system
 */

import * as React from 'react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { EnhancedToastProps } from '@/types/toast';
import { RefreshButton } from './RefreshButton';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Info,
  X
} from 'lucide-react';

// Toast type to icon mapping
const TOAST_ICONS = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info
} as const;

// Toast type to styling mapping
const TOAST_STYLES = {
  success: {
    container: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200',
    icon: 'text-green-600 dark:text-green-400'
  },
  error: {
    container: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
    icon: 'text-red-600 dark:text-red-400'
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',
    icon: 'text-yellow-600 dark:text-yellow-400'
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200',
    icon: 'text-blue-600 dark:text-blue-400'
  }
} as const;

interface EnhancedToastComponentProps extends EnhancedToastProps {
  /** Toast instance from Sonner */
  t?: any;
  /** Whether to show close button */
  showCloseButton?: boolean;
}

const EnhancedToastComponent = React.forwardRef<
  HTMLDivElement,
  EnhancedToastComponentProps
>((
  {
    message,
    type,
    showRefreshButton = false,
    refreshButton,
    dismissible = true,
    className,
    id,
    t,
    showCloseButton = true,
    ...props
  },
  ref
) => {
  const IconComponent = TOAST_ICONS[type];
  const styles = TOAST_STYLES[type];

  const handleDismiss = React.useCallback(() => {
    if (id) {
      toast.dismiss(id);
    } else if (t?.id) {
      toast.dismiss(t.id);
    }
  }, [id, t]);

  return (
    <div
      ref={ref}
      className={cn(
        'flex items-center justify-between p-4 rounded-lg shadow-lg border',
        'min-w-[300px] max-w-[500px]',
        'transition-all duration-200 ease-in-out',
        'animate-in slide-in-from-top-2 fade-in-0',
        styles.container,
        className
      )}
      role="alert"
      aria-live="polite"
      {...props}
    >
      {/* Icon and Message */}
      <div className="flex items-center flex-1 min-w-0">
        <IconComponent 
          className={cn('h-5 w-5 flex-shrink-0 mr-3', styles.icon)}
          aria-hidden="true"
        />
        <span className="text-sm font-medium truncate pr-2">
          {message}
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2 ml-3">
        {/* Refresh Button */}
        {showRefreshButton && refreshButton && (
          <RefreshButton
            onRefresh={refreshButton.onRefresh}
            loading={refreshButton.loading}
            disabled={refreshButton.disabled}
            ariaLabel={refreshButton.ariaLabel || 'Refresh chapter list'}
            variant="ghost"
            size="icon"
            className={cn(
              'h-8 w-8 hover:bg-white/20 focus:bg-white/20',
              'transition-colors duration-150',
              refreshButton.className
            )}
          />
        )}

        {/* Close Button */}
        {dismissible && showCloseButton && (
          <button
            onClick={handleDismiss}
            className={cn(
              'h-8 w-8 rounded-md transition-colors duration-150',
              'hover:bg-white/20 focus:bg-white/20',
              'focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500',
              'flex items-center justify-center'
            )}
            aria-label="Dismiss notification"
          >
            <X className="h-4 w-4" aria-hidden="true" />
          </button>
        )}
      </div>
    </div>
  );
});

EnhancedToastComponent.displayName = 'EnhancedToastComponent';

/**
 * Creates and displays an enhanced toast notification
 */
export function showEnhancedToast(props: EnhancedToastProps): string {
  const {
    message,
    type,
    duration = 0,
    showRefreshButton = false,
    refreshButton,
    dismissible = true,
    className,
    id: customId
  } = props;

  const toastId = customId || `enhanced-toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const sonnerToastId = toast.custom(
    (t) => (
      <EnhancedToastComponent
        message={message}
        type={type}
        showRefreshButton={showRefreshButton}
        refreshButton={refreshButton}
        dismissible={dismissible}
        className={className}
        id={toastId}
        t={t}
      />
    ),
    {
      duration: duration || undefined,
      id: toastId,
      position: 'top-right'
    }
  );

  return toastId;
}

/**
 * Creates a success toast with refresh button
 */
export function showSuccessToastWithRefresh(
  message: string,
  onRefresh: () => void,
  options: Partial<EnhancedToastProps> = {}
): string {
  return showEnhancedToast({
    message,
    type: 'success',
    duration: 2000,
    showRefreshButton: true,
    refreshButton: {
      onRefresh,
      loading: false,
      disabled: false,
      ariaLabel: 'Refresh chapter list'
    },
    ...options
  });
}

/**
 * Creates an error toast
 */
export function showErrorToast(
  message: string,
  options: Partial<EnhancedToastProps> = {}
): string {
  return showEnhancedToast({
    message,
    type: 'error',
    duration: 0, // No auto-dismiss for errors
    showRefreshButton: false,
    ...options
  });
}

/**
 * Creates an info/loading toast
 */
export function showInfoToast(
  message: string,
  options: Partial<EnhancedToastProps> = {}
): string {
  return showEnhancedToast({
    message,
    type: 'info',
    duration: 0, // No auto-dismiss for loading states
    showRefreshButton: false,
    ...options
  });
}

export { EnhancedToastComponent, type EnhancedToastComponentProps };