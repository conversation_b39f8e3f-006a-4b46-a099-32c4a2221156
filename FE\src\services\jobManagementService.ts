import { BaseService, API_ENDPOINTS, PaginationParams } from './BaseService';
import {
  Job,
  JobListResponse,
  JobHistoryResponse as JobHistoryResponseType,
  JobCreateRequest,
  JobUpdateRequest,
  JobFilter,
  JobStatus,
  JobPriority,
  JobType
} from '@/types/jobManagement';

// Legacy Job History Response interface (keep for backward compatibility)
export interface JobHistoryResponse {
  data: JobStatusResponse[];
  pagination: {
    page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

// Legacy Types (keep for backward compatibility)
export interface JobStatusResponse {
  success: boolean;
  message: string;
  timestamp: string;
  job_id: string;
  job_type: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  progress_percentage: number;
  total_items?: number;
  completed_items: number;
  failed_items: number;
  errors: string[];
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
}

// Job Management Service
class JobManagementService extends BaseService {
  constructor() {
    super();
  }

  async getJobStatus(jobId: string, jobType?: string): Promise<JobStatusResponse> {
    const params = jobType ? { job_type: jobType } : {};
    return this.get<JobStatusResponse>(`${API_ENDPOINTS.JOBS}/${jobId}/status`, { params });
  }

  async cancelJob(jobId: string, jobType?: string): Promise<{ message: string }> {
    const url = jobType 
      ? `${API_ENDPOINTS.JOBS}/${jobId}/cancel?job_type=${jobType}`
      : `${API_ENDPOINTS.JOBS}/${jobId}/cancel`;
    return this.post<{ message: string }>(url, {});
  }

  async getActiveJobs(jobType?: string): Promise<JobStatusResponse[]> {
    const params = jobType ? { job_type: jobType } : {};
    return this.get<JobStatusResponse[]>(`${API_ENDPOINTS.JOBS}/active`, { params });
  }

  async getJobHistory(params?: PaginationParams & { job_type?: string; status?: string }): Promise<JobHistoryResponse> {
    return this.get<JobHistoryResponse>(`${API_ENDPOINTS.JOBS}/history`, { params });
  }

  async getJobDetails(jobId: string): Promise<JobStatusResponse> {
    return this.get<JobStatusResponse>(`${API_ENDPOINTS.JOBS}/${jobId}`);
  }

  async deleteJob(jobId: string): Promise<{ message: string }> {
    return this.delete<{ message: string }>(`${API_ENDPOINTS.JOBS}/${jobId}`);
  }

  async getJobLogs(jobId: string, params?: { level?: string; limit?: number }): Promise<{ logs: any[] }> {
    return this.get<{ logs: any[] }>(`${API_ENDPOINTS.JOBS}/${jobId}/logs`, { params });
  }

  async retryJob(jobId: string): Promise<{ message: string; new_job_id: string }> {
    return this.post<{ message: string; new_job_id: string }>(`${API_ENDPOINTS.JOBS}/${jobId}/retry`);
  }

  async bulkCancelJobs(request: { job_ids: string[]; job_type?: string }): Promise<{ cancelled_count: number; failed_count: number }> {
    return this.post<{ cancelled_count: number; failed_count: number }>(`${API_ENDPOINTS.JOBS}/bulk-cancel`, request);
  }

  // ============================================================================
  // New Job Management UI Methods
  // ============================================================================

  /**
   * Get list of jobs with new Job interface and filtering
   */
  async getJobs(
    page: number = 1,
    limit: number = 10,
    filters?: JobFilter
  ): Promise<JobListResponse> {
    const params: any = {
      page,
      limit
    };

    // Add filters to params
    if (filters) {
      if (filters.status && filters.status.length > 0) {
        params.status = filters.status.join(',');
      }
      if (filters.priority && filters.priority.length > 0) {
        params.priority = filters.priority.join(',');
      }
      if (filters.dateFrom) {
        params.date_from = filters.dateFrom;
      }
      if (filters.dateTo) {
        params.date_to = filters.dateTo;
      }
      if (filters.search) {
        params.search = filters.search;
      }
    }

    return this.get<JobListResponse>(`${API_ENDPOINTS.JOBS}`, { params });
  }

  /**
   * Get job history with new interface
   */
  async getJobHistoryNew(
    page: number = 1,
    limit: number = 10,
    filters?: JobFilter
  ): Promise<JobHistoryResponseType> {
    const params: any = {
      page,
      limit
    };

    // Add filters to params
    if (filters) {
      if (filters.status && filters.status.length > 0) {
        params.status = filters.status.join(',');
      }
      if (filters.dateFrom) {
        params.date_from = filters.dateFrom;
      }
      if (filters.dateTo) {
        params.date_to = filters.dateTo;
      }
      if (filters.search) {
        params.search = filters.search;
      }
    }

    return this.get<JobHistoryResponseType>(`${API_ENDPOINTS.JOBS}/history`, { params });
  }

  /**
   * Create a new job with new interface
   */
  async createJobNew(jobData: JobCreateRequest): Promise<Job> {
    const requestData = {
      url: jobData.url,
      page_limit: jobData.pageLimit,
      scraping_mode: jobData.scrapingMode || 'traditional',
      title: jobData.title,
      description: jobData.description,
      priority: jobData.priority || JobPriority.MEDIUM
    };

    return this.post<Job>(`${API_ENDPOINTS.SCRAPING}/async`, requestData);
  }

  /**
   * Update a job with new interface
   */
  async updateJobNew(jobId: string, updates: JobUpdateRequest): Promise<Job> {
    return this.put<Job>(`${API_ENDPOINTS.JOBS}/${jobId}`, updates);
  }

  /**
   * Get job statistics
   */
  async getJobStats(): Promise<{
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  }> {
    return this.get<{
      total: number;
      pending: number;
      running: number;
      completed: number;
      failed: number;
      cancelled: number;
    }>(`${API_ENDPOINTS.JOBS}/stats`);
  }

  /**
   * Transform legacy JobStatusResponse to new Job interface
   */
  private transformLegacyJob(legacyJob: JobStatusResponse): Job {
    return {
      id: legacyJob.job_id,
      title: legacyJob.job_type || 'Scraping Job',
      description: legacyJob.message,
      type: JobType.SCRAPING, // Default to scraping type for legacy jobs
      status: this.mapJobStatus(legacyJob.status),
      priority: JobPriority.MEDIUM,
      progress: legacyJob.progress_percentage,
      createdAt: legacyJob.created_at,
      updatedAt: legacyJob.timestamp,
      startedAt: legacyJob.started_at,
      completedAt: legacyJob.completed_at,
      errorMessage: legacyJob.errors.length > 0 ? legacyJob.errors.join(', ') : undefined,
      metadata: {
        totalItems: legacyJob.total_items,
        completedItems: legacyJob.completed_items,
        failedItems: legacyJob.failed_items,
        estimatedCompletion: legacyJob.estimated_completion
      }
    };
  }

  /**
   * Map legacy status to new JobStatus enum
   */
  private mapJobStatus(status: string): JobStatus {
    switch (status.toLowerCase()) {
      case 'pending':
        return JobStatus.PENDING;
      case 'in_progress':
        return JobStatus.RUNNING;
      case 'completed':
        return JobStatus.COMPLETED;
      case 'failed':
        return JobStatus.FAILED;
      case 'cancelled':
        return JobStatus.CANCELLED;
      default:
        return JobStatus.PENDING;
    }
  }
}

export const jobManagementService = new JobManagementService();