'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { ScrapeResponse } from '@/services/scrapingService';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { toast } from 'sonner';

interface ScrapingProgressProps {
  status: 'idle' | 'testing' | 'scraping' | 'completed' | 'error';
  result: ScrapeResponse | null;
  error: string | null;
  onViewStory?: () => void;
}

/**
 * Internal component wrapped with error boundary
 */
function ScrapingProgressContent({ 
  status, 
  result, 
  error, 
  onViewStory 
}: ScrapingProgressProps) {
  // Handle view story action with simple error handling
  const handleViewStory = () => {
    try {
      if (!result?.story_id) {
        throw new Error('Cannot view story: Story ID is missing');
      }
      onViewStory?.();
    } catch (err) {
      toast.error('Failed to navigate to story');
    }
  };

  // Format error message for display with user-friendly messages
  const getErrorMessage = (error: string | null): string => {
    if (!error) return 'An unexpected error occurred';
    
    // Convert technical errors to user-friendly messages
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return 'Connection failed. Please check your internet connection and try again.';
    }
    if (errorLower.includes('timeout')) {
      return 'Request timed out. The server might be busy, please try again in a few minutes.';
    }
    if (errorLower.includes('404') || errorLower.includes('not found')) {
      return 'Story not found. Please check the URL and make sure it\'s a valid story page.';
    }
    if (errorLower.includes('403') || errorLower.includes('forbidden')) {
      return 'Access denied. The website might be blocking our requests. Please try again later.';
    }
    if (errorLower.includes('500') || errorLower.includes('server error')) {
      return 'Server error occurred. Please try again in a few minutes.';
    }
    if (errorLower.includes('invalid url') || errorLower.includes('malformed')) {
      return 'Invalid URL format. Please enter a valid webtruyen.diendantruyen.com story URL.';
    }
    if (errorLower.includes('rate limit') || errorLower.includes('too many requests')) {
      return 'Too many requests. Please wait a few minutes before trying again.';
    }
    
    // Return simplified version of the original error
    return error.length > 100 ? error.substring(0, 100) + '...' : error;
  };
  
  // Get actionable suggestions based on error type
  const getErrorSuggestions = (error: string | null): string[] => {
    if (!error) return ['Try refreshing the page', 'Contact support if the issue persists'];
    
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return ['Check your internet connection', 'Try again in a few moments', 'Refresh the page'];
    }
    if (errorLower.includes('timeout')) {
      return ['Wait a few minutes and try again', 'Try with fewer pages', 'Check if the website is accessible'];
    }
    if (errorLower.includes('404') || errorLower.includes('not found')) {
      return ['Verify the story URL is correct', 'Make sure the story exists on the website', 'Copy the URL directly from your browser'];
    }
    if (errorLower.includes('403') || errorLower.includes('forbidden')) {
      return ['Try again in 10-15 minutes', 'Use a different browser', 'Check if the website is accessible'];
    }
    if (errorLower.includes('rate limit')) {
      return ['Wait 5-10 minutes before trying again', 'Try with fewer pages', 'Use traditional mode instead of real-time'];
    }
    
    return ['Try refreshing the page', 'Try again with different settings', 'Contact support if the issue persists'];
  };


  if (status === 'idle') {
    return null;
  }

  return (
    <Card className="w-full">
      <CardContent className="pt-6 space-y-4">
        {/* Scraping in Progress */}
        {status === 'scraping' && (
          <div className="space-y-6 animate-in fade-in-0 slide-in-from-bottom-4 duration-500">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 hover:shadow-lg transition-all duration-300 group">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Loader2 className="h-6 w-6 text-blue-600 animate-spin group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 h-6 w-6 border-2 border-blue-200 rounded-full animate-ping" />
                  <div className="absolute inset-0 h-6 w-6 border border-blue-300 rounded-full animate-pulse" />
                </div>
                <div className="space-y-1">
                  <span className="font-semibold text-blue-900 group-hover:text-blue-800 transition-colors duration-200">Scraping in progress...</span>
                  <p className="text-sm text-blue-700 animate-pulse">This may take a few minutes</p>
                </div>
              </div>
              <Badge className="bg-blue-500 hover:bg-blue-600 transition-all duration-200 animate-pulse hover:scale-105 cursor-default">
                Running
              </Badge>
            </div>
            
            <div className="space-y-4 animate-in fade-in-0 duration-700 delay-200">
              <div className="flex justify-between items-center group">
                <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-200">Progress</span>
                <span className="text-sm text-muted-foreground animate-pulse flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}} />
                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}} />
                  Processing...
                </span>
              </div>
              <div className="relative group">
                <Progress value={undefined} className="w-full h-4 transition-all duration-300 group-hover:h-5" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10 animate-pulse" />
              </div>
              <div className="text-center p-4 bg-gradient-to-r from-muted/20 to-muted/30 rounded-lg border border-border/50 hover:shadow-md transition-all duration-300 group">
                <p className="text-sm text-muted-foreground flex items-center justify-center gap-2 group-hover:text-foreground transition-colors duration-200">
                  <span className="animate-spin">🔄</span>
                  Please wait while we scrape the story content
                </p>
                <p className="text-xs text-muted-foreground mt-2 opacity-80 group-hover:opacity-100 transition-opacity duration-200">
                  💡 You can safely navigate away and return to check progress
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {status === 'error' && error && (
          <Alert variant="destructive" className="border-red-200 bg-gradient-to-r from-red-50 to-rose-50 animate-in fade-in-0 slide-in-from-bottom-4 duration-500 hover:shadow-lg transition-all duration-300">
            <div className="relative">
              <AlertCircle className="h-5 w-5 text-red-600 animate-pulse" />
              <div className="absolute inset-0 h-5 w-5 border-2 border-red-300 rounded-full animate-ping opacity-30" />
            </div>
            <AlertDescription>
              <div className="space-y-3">
                <div className="animate-in fade-in-0 duration-300 delay-100">
                  <h4 className="font-semibold text-red-800 mb-1 flex items-center gap-2">
                    ❌ Scraping Failed
                  </h4>
                  <p className="text-red-700">{getErrorMessage(error)}</p>
                </div>
                
                <div className="space-y-2 animate-in fade-in-0 duration-300 delay-200">
                  <h5 className="font-medium text-red-800 text-sm flex items-center gap-2">
                    💡 What you can try:
                  </h5>
                  <ul className="space-y-2">
                    {getErrorSuggestions(error).map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-3 text-sm text-red-700 animate-in fade-in-0 duration-300 hover:bg-red-100/50 p-2 rounded-md transition-all duration-200 group" style={{animationDelay: `${(index + 3) * 100}ms`}}>
                        <span className="text-red-500 mt-0.5 group-hover:scale-125 transition-transform duration-200">•</span>
                        <span className="group-hover:text-red-800 transition-colors duration-200">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="pt-3 border-t border-red-200 animate-in fade-in-0 duration-300 delay-500">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => window.location.reload()}
                    className="border-red-300 text-red-700 hover:bg-red-100 hover:border-red-400 transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md"
                  >
                    🔄 Try Again
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Success Results */}
        {status === 'completed' && result && (
          <Alert className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 animate-in fade-in-0 slide-in-from-bottom-4 duration-500">
            <div className="relative">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div className="absolute inset-0 h-6 w-6 border-2 border-green-300 rounded-full animate-ping" />
            </div>
            <AlertDescription className="text-green-800">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-bold text-xl text-green-900 flex items-center gap-2">
                    🎉 Scraping Completed!
                  </h4>
                  <p className="font-semibold text-lg text-green-800">
                    {result.title}
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 p-4 bg-white/50 rounded-lg border border-green-200">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-green-900">📄 Pages scraped:</span> 
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {result.pages_scraped}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-green-900">📚 Chapters found:</span> 
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {result.chapters_created}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-green-900">📊 Total pages:</span> 
                      <Badge variant="outline" className="border-green-300 text-green-700">
                        {result.total_pages}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-green-900">📖 Total chapters:</span> 
                      <Badge variant="outline" className="border-green-300 text-green-700">
                        {result.total_chapters}
                      </Badge>
                    </div>
                  </div>
                </div>

                {result.story_id && (
                  <div className="pt-2">
                    <Button
                      size="lg"
                      onClick={handleViewStory}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Story
                    </Button>
                  </div>
                )}

                {result.message && (
                  <div className="p-3 bg-green-100 rounded-md border border-green-200">
                    <p className="text-sm text-green-700 font-medium">
                      ℹ️ {result.message}
                    </p>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * ScrapingProgress component with error boundary
 */
export default function ScrapingProgress(props: ScrapingProgressProps) {
  return (
    <ErrorBoundary
      fallback={({ error }) => (
        <Card className="w-full">
          <CardContent className="pt-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Failed to display scraping progress</p>
                  <p className="text-sm text-muted-foreground">
                    {error?.message || 'An unexpected error occurred'}
                  </p>
                  <Button size="sm" variant="outline" onClick={() => window.location.reload()}>
                    Try Again
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
      onError={(error) => {
        console.error('ScrapingProgress Error:', error);
        toast.error('Scraping progress display encountered an error');
      }}
    >
      <ScrapingProgressContent {...props} />
    </ErrorBoundary>
  );
}