'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

import { useChapterManagement } from '@/hooks/useChapterManagement/useChapterManagement';
import { ChapterListHeader } from './ChapterListHeader';
import { BatchActionsPanel } from './BatchActionsPanel';
import { ChapterList } from './ChapterList';
import EnhancedPagination from '@/components/ui/enhanced-pagination';
import { useBatchActions } from '@/hooks/useBatchActions';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { toast } from 'sonner';
import { useRefreshContextSafe } from '../StoryDetailWrapper';

import { Chapter } from '@/types/story';

interface StoryChapterListProps {
  storyId: string;
  initialChapters: Chapter[];
}

/**
 * Internal component wrapped with error boundary
 */
function StoryChapterListContent({ storyId, initialChapters }: StoryChapterListProps) {
  const router = useRouter();
  const [selectionMode, setSelectionMode] = useState<'scraping' | 'enhancement' | null>(null);
  const chapterListRef = useRef<HTMLDivElement>(null);
  
  // Get refresh functionality from context with graceful degradation
  const refreshContext = useRefreshContextSafe();
  
  // Use refreshKey to force re-render when refresh is triggered
  const refreshKey = refreshContext.refreshKey || 0;

  const {
    chapters,
    chaptersLoading,
    totalChapters,
    filteredChapters,
    currentPage,
    totalPages,
    filter,
    setFilter,
    selectedChapters,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
    handlePageChange,
    chapterStats,
    paginationLoading,
  } = useChapterManagement(storyId, initialChapters, refreshKey);
  
  // Debug: Track chapters changes and component re-renders
  useEffect(() => {
    // Component re-rendered tracking removed
  }, [chapters, currentPage, chaptersLoading, filteredChapters.length]);
  
  // Debug: Simple render tracking
  // Render tracking removed
  
  // Debug: Track component mount/unmount
  useEffect(() => {
    // Component mounted tracking
    return () => {
      // Component unmounting tracking
    };
  }, [storyId]);
  
  // Debug: Track refreshKey changes
  useEffect(() => {
    // RefreshKey change tracking removed
  }, [refreshKey, storyId]);

  const { 
    batchScrapingState, 
    batchEnhancementState, 
    handleBatchScrape,
    handleBatchEnhance, 
    cancelAction
  } = useBatchActions({
    onComplete: () => {
      setSelectionMode(null);
      handleDeselectAll();
    },
    // Only pass onRefresh if it's available and the refresh functionality is available
    getOnRefresh: refreshContext.isAvailable ? () => refreshContext.onRefresh : undefined
  });

  const handleStartAction = async (chapterIds: string[]) => {
    if (selectionMode === 'scraping') {
      try {
        // Use the enhanced handleBatchScrape from useBatchActions hook
        await handleBatchScrape(storyId, chapterIds);
        
        // Note: Navigation to scraping progress page is handled separately
        // since the current handleBatchScrape doesn't return job_id yet
        // This will be improved in future iterations
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Batch scraping failed';
        toast.error(errorMessage);
      }
    } else if (selectionMode === 'enhancement') {
      try {
        // Use the enhanced handleBatchEnhance from useBatchActions hook
        await handleBatchEnhance(storyId, chapterIds);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Enhancement failed';
        toast.error(errorMessage);
      }
    }
  };

  const handleCancelAction = () => {
    try {
      const jobId = selectionMode === 'scraping' ? batchScrapingState.jobId : batchEnhancementState.jobId;
      if (jobId) {
        cancelAction(jobId);
        // Toast notification is handled by the cancelAction function in useBatchActions
      } else {
        setSelectionMode(null);
        handleDeselectAll();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Cancel action failed';
      toast.error(errorMessage);
    }
  };

  useEffect(() => {
    handleDeselectAll();
  }, [selectionMode, handleDeselectAll]);

  const isProcessing = batchScrapingState.isActive || batchEnhancementState.isActive;
  const currentProgress = selectionMode === 'scraping' 
    ? batchScrapingState.progress
    : batchEnhancementState.progress;

  const progressPercentage = currentProgress?.progress_percentage ?? 0;

  return (
    <div className="space-y-4" data-chapter-list>
      <div ref={chapterListRef}>
          <ChapterListHeader
            stats={chapterStats}
            filter={filter}
            onFilterChange={setFilter}
            selectionMode={selectionMode}
            setSelectionMode={setSelectionMode}
          />
        </div>

      {selectionMode && (
        <BatchActionsPanel
          selectionMode={selectionMode}
          isProcessing={isProcessing}
          progress={progressPercentage}
          onStartAction={handleStartAction}
          onCancel={handleCancelAction}
          selectedCount={selectedChapters.size}
          selectedChapterIds={chapters.filter(c => selectedChapters.has(c.id)).map(c => c.id)}
        />
      )}

      <div>
        <ChapterList
          key={`chapters-${storyId}-${refreshKey}`} // Force re-render when refreshKey changes
          storyId={storyId}
          chapters={filteredChapters}
          loading={chaptersLoading}
          selectionMode={selectionMode}
          selectedChapters={selectedChapters}
          onSelectChapter={handleSelectChapter}
          onSelectAll={handleSelectAll}
          onDeselectAll={handleDeselectAll}
        />
        
        <EnhancedPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          loading={paginationLoading}
          scrollToTopOnChange={true}
          scrollTargetRef={chapterListRef}
          className="mt-8"
          showPageNumbers={true}
          maxVisiblePages={7}
        />
      </div>
    </div>
  );
};

/**
 * Main component with error boundary
 */
const StoryChapterList: React.FC<StoryChapterListProps> = ({ storyId, initialChapters }) => {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="p-6 text-center space-y-4">
          <h3 className="text-lg font-semibold text-red-600">Chapter List Error</h3>
          <p className="text-muted-foreground">
            {error?.message || 'An unexpected error occurred'}
          </p>
          <div className="flex gap-2 justify-center">
            <button
              onClick={resetError}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Try Again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Reload Page
            </button>
          </div>
        </div>
      )}
      onError={(error) => {
        toast.error('Chapter list encountered an error');
      }}
    >
      <StoryChapterListContent storyId={storyId} initialChapters={initialChapters} />
    </ErrorBoundary>
  );
};

export default StoryChapterList;