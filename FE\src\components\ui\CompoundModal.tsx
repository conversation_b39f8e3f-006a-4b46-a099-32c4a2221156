'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Search, Filter } from 'lucide-react';

// Modal context
interface ModalContextValue {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const ModalContext = React.createContext<ModalContextValue>({
  open: false,
  setOpen: () => {}
});

// Root modal component
interface CompoundModalRootProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  defaultOpen?: boolean;
}

const CompoundModalRoot: React.FC<CompoundModalRootProps> = ({
  children,
  open: controlledOpen,
  onOpenChange,
  defaultOpen = false
}) => {
  const [internalOpen, setInternalOpen] = React.useState(defaultOpen);
  
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const contextValue = {
    open,
    setOpen
  };

  return (
    <ModalContext.Provider value={contextValue}>
      <Dialog open={open} onOpenChange={setOpen}>
        {children}
      </Dialog>
    </ModalContext.Provider>
  );
};

// Trigger component
interface CompoundModalTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
  className?: string;
}

const CompoundModalTrigger: React.FC<CompoundModalTriggerProps> = ({
  children,
  asChild = false,
  className
}) => {
  return (
    <DialogTrigger asChild={asChild} className={className}>
      {children}
    </DialogTrigger>
  );
};

// Content wrapper
interface CompoundModalContentProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  maxHeight?: string;
}

const CompoundModalContent: React.FC<CompoundModalContentProps> = ({
  children,
  className,
  size = 'lg',
  maxHeight = '80vh'
}) => {
  const sizeClasses = {
    sm: 'sm:max-w-md',
    md: 'sm:max-w-lg',
    lg: 'sm:max-w-2xl',
    xl: 'sm:max-w-4xl',
    full: 'sm:max-w-[95vw]'
  };

  return (
    <DialogContent 
      className={cn(
        sizeClasses[size],
        'flex flex-col',
        className
      )}
      style={{ maxHeight }}
    >
      {children}
    </DialogContent>
  );
};

// Header component
interface CompoundModalHeaderProps {
  title: string;
  description?: string;
  icon?: React.ComponentType<{ size?: number; className?: string }>;
  className?: string;
}

const CompoundModalHeader: React.FC<CompoundModalHeaderProps> = ({
  title,
  description,
  icon: Icon,
  className
}) => {
  return (
    <DialogHeader className={className}>
      <DialogTitle className="flex items-center gap-2">
        {Icon && <Icon size={18} />}
        {title}
      </DialogTitle>
      {description && (
        <DialogDescription>{description}</DialogDescription>
      )}
    </DialogHeader>
  );
};

// Search and filter controls
interface CompoundModalControlsProps {
  children: React.ReactNode;
  className?: string;
}

const CompoundModalControls: React.FC<CompoundModalControlsProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('space-y-4 border-b pb-4', className)}>
      {children}
    </div>
  );
};

// Search input component
interface CompoundModalSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const CompoundModalSearch: React.FC<CompoundModalSearchProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  className
}) => {
  return (
    <div className={cn('relative', className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10"
      />
    </div>
  );
};

// Filter component
interface CompoundModalFilterProps {
  children: React.ReactNode;
  label?: string;
  className?: string;
}

const CompoundModalFilter: React.FC<CompoundModalFilterProps> = ({
  children,
  label,
  className
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {label && (
        <>
          <Filter size={16} className="text-gray-400" />
          <span className="text-sm font-medium">{label}:</span>
        </>
      )}
      {children}
    </div>
  );
};

// Body/content area
interface CompoundModalBodyProps {
  children: React.ReactNode;
  className?: string;
  scrollable?: boolean;
}

const CompoundModalBody: React.FC<CompoundModalBodyProps> = ({
  children,
  className,
  scrollable = true
}) => {
  return (
    <div className={cn(
      'flex-1 min-h-0',
      {
        'overflow-y-auto': scrollable
      },
      className
    )}>
      {children}
    </div>
  );
};

// Footer component
interface CompoundModalFooterProps {
  children: React.ReactNode;
  className?: string;
  justify?: 'start' | 'center' | 'end' | 'between';
}

const CompoundModalFooter: React.FC<CompoundModalFooterProps> = ({
  children,
  className,
  justify = 'end'
}) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div className={cn(
      'flex items-center gap-2 border-t pt-4',
      justifyClasses[justify],
      className
    )}>
      {children}
    </div>
  );
};

// Close button component
interface CompoundModalCloseProps {
  children?: React.ReactNode;
  variant?: 'default' | 'outline' | 'ghost';
  className?: string;
}

const CompoundModalClose: React.FC<CompoundModalCloseProps> = ({
  children = 'Close',
  variant = 'outline',
  className
}) => {
  const { setOpen } = React.useContext(ModalContext);

  return (
    <Button
      variant={variant}
      onClick={() => setOpen(false)}
      className={className}
    >
      {children}
    </Button>
  );
};

// Stats/info component
interface CompoundModalStatsProps {
  items: Array<{ label: string; value: string | number }>;
  className?: string;
}

const CompoundModalStats: React.FC<CompoundModalStatsProps> = ({
  items,
  className
}) => {
  return (
    <div className={cn('flex items-center gap-4 text-sm text-muted-foreground', className)}>
      {items.map((item, index) => (
        <span key={index}>
          <span className="font-medium">{item.label}:</span> {item.value}
        </span>
      ))}
    </div>
  );
};

// Export compound component
export const CompoundModal = {
  Root: CompoundModalRoot,
  Trigger: CompoundModalTrigger,
  Content: CompoundModalContent,
  Header: CompoundModalHeader,
  Controls: CompoundModalControls,
  Search: CompoundModalSearch,
  Filter: CompoundModalFilter,
  Body: CompoundModalBody,
  Footer: CompoundModalFooter,
  Close: CompoundModalClose,
  Stats: CompoundModalStats
};

export default CompoundModal;