"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vaul";
exports.ids = ["vendor-chunks/vaul"];
exports.modules = {

/***/ "(ssr)/./node_modules/vaul/dist/index.mjs":
/*!******************************************!*\
  !*** ./node_modules/vaul/dist/index.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Drawer: () => (/* binding */ Drawer),\n/* harmony export */   Handle: () => (/* binding */ Handle),\n/* harmony export */   NestedRoot: () => (/* binding */ NestedRoot),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Drawer,Handle,NestedRoot,Overlay,Portal,Root auto */ function __insertCSS(code) {\n    if (!code || typeof document == \"undefined\") return;\n    let head = document.head || document.getElementsByTagName(\"head\")[0];\n    let style = document.createElement(\"style\");\n    style.type = \"text/css\";\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\n\nconst DrawerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    handleOnly: false,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    closeDrawer: ()=>{},\n    direction: \"bottom\",\n    shouldAnimate: {\n        current: true\n    },\n    shouldScaleBackground: false,\n    setBackgroundColorOnScale: true,\n    noBodyStyles: false,\n    container: null,\n    autoFocus: false\n});\nconst useDrawerContext = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DrawerContext);\n    if (!context) {\n        throw new Error(\"useDrawerContext must be used within a Drawer.Root\");\n    }\n    return context;\n};\n__insertCSS(\"[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\\n[data-state=closed]\\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}\");\nfunction isMobileFirefox() {\n    const userAgent = navigator.userAgent;\n    return  false && (0 // iOS Firefox\n    );\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isSafari() {\n    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return  false ? 0 : undefined;\n}\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst KEYBOARD_BUFFER = 24;\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction chain$1(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === \"function\") {\n                callback(...args);\n            }\n        }\n    };\n}\n// @ts-ignore\nconst visualViewport = typeof document !== \"undefined\" && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    \"checkbox\",\n    \"radio\",\n    \"range\",\n    \"color\",\n    \"file\",\n    \"image\",\n    \"button\",\n    \"submit\",\n    \"reset\"\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect(()=>{\n        if (isDisabled) {\n            return;\n        }\n        preventScrollCount++;\n        if (preventScrollCount === 1) {\n            if (isIOS()) {\n                restore = preventScrollMobileSafari();\n            }\n        }\n        return ()=>{\n            preventScrollCount--;\n            if (preventScrollCount === 0) {\n                restore == null ? void 0 : restore();\n            }\n        };\n    }, [\n        isDisabled\n    ]);\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = \"translateY(-2000px)\";\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = \"translateY(-2000px)\";\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener(\"resize\", ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain$1(setStyle(document.documentElement, \"paddingRight\", `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain$1(addEvent(document, \"touchstart\", onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchmove\", onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchend\", onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"focus\", onFocus, true), addEvent(window, \"scroll\", onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    // https://github.com/microsoft/TypeScript/issues/17827#issuecomment-391663310\n    // @ts-ignore\n    let cur = element.style[style];\n    // @ts-ignore\n    element.style[style] = value;\n    return ()=>{\n        // @ts-ignore\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            // Buffer is needed for some edge cases\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom + KEYBOARD_BUFFER;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith(\"--\")) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    {\n        el.style[prop] = originalStyles[prop];\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case \"top\":\n        case \"bottom\":\n            return true;\n        case \"left\":\n        case \"right\":\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(\", \")[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(\", \")[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nfunction assignStyle(element, style) {\n    if (!element) return ()=>{};\n    const prevStyle = element.style.cssText;\n    Object.assign(element.style, style);\n    return ()=>{\n        element.style.cssText = prevStyle;\n    };\n}\n/**\n * Receives functions as arguments and returns a new function that calls all.\n */ function chain(...fns) {\n    return (...args)=>{\n        for (const fn of fns){\n            if (typeof fn === \"function\") {\n                // @ts-ignore\n                fn(...args);\n            }\n        }\n    };\n}\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = \"vaul-dragging\";\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args), []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            handleChange(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const setter = nextValue;\n            const value = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n            if (value !== prop) handleChange(value);\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = \"bottom\", container, snapToSequentialPoint }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const [windowDimensions, setWindowDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState( false ? 0 : undefined);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        function onResize() {\n            setWindowDimensions({\n                innerWidth: window.innerWidth,\n                innerHeight: window.innerHeight\n            });\n        }\n        window.addEventListener(\"resize\", onResize);\n        return ()=>window.removeEventListener(\"resize\", onResize);\n    }, []);\n    const isLastSnapPoint = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const activeSnapPointIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var _snapPoints_findIndex;\n        return (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : null;\n    }, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const snapPointsOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const containerSize = container ? {\n            width: container.getBoundingClientRect().width,\n            height: container.getBoundingClientRect().height\n        } :  false ? 0 : {\n            width: 0,\n            height: 0\n        };\n        var _snapPoints_map;\n        return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map((snapPoint)=>{\n            const isPx = typeof snapPoint === \"string\";\n            let snapPointAsNumber = 0;\n            if (isPx) {\n                snapPointAsNumber = parseInt(snapPoint, 10);\n            }\n            if (isVertical(direction)) {\n                const height = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.height : 0;\n                if (windowDimensions) {\n                    return direction === \"bottom\" ? containerSize.height - height : -containerSize.height + height;\n                }\n                return height;\n            }\n            const width = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.width : 0;\n            if (windowDimensions) {\n                return direction === \"right\" ? containerSize.width - width : -containerSize.width + width;\n            }\n            return width;\n        })) != null ? _snapPoints_map : [];\n    }, [\n        snapPoints,\n        windowDimensions,\n        container\n    ]);\n    const activeSnapPointOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null, [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dimension)=>{\n        var _snapPointsOffset_findIndex;\n        const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex((snapPointDim)=>snapPointDim === dimension)) != null ? _snapPointsOffset_findIndex : null;\n        onSnapPointChange(newSnapPointIndex);\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n        });\n        if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && fadeFromIndex !== undefined && newSnapPointIndex !== fadeFromIndex && newSnapPointIndex < fadeFromIndex) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"0\"\n            });\n        } else {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"1\"\n            });\n        }\n        setActiveSnapPoint(snapPoints == null ? void 0 : snapPoints[Math.max(newSnapPointIndex, 0)]);\n    }, [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (activeSnapPoint || activeSnapPointProp) {\n            var _snapPoints_findIndex;\n            const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : -1;\n            if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === \"number\") {\n                snapToPoint(snapPointsOffset[newIndex]);\n            }\n        }\n    }, [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === \"bottom\" || direction === \"right\" ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            });\n        }\n        if (!snapToSequentialPoint && velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (!snapToSequentialPoint && velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== \"number\" || typeof curr !== \"number\") return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint && snapPoints) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === \"bottom\" || direction === \"right\" ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === \"bottom\" || direction === \"right\") && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === \"top\" || direction === \"left\") && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== \"number\" || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\nconst noop = ()=>()=>{};\nfunction useScaleBackground() {\n    const { direction, isOpen, shouldScaleBackground, setBackgroundColorOnScale, noBodyStyles } = useDrawerContext();\n    const timeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const initialBackgroundColor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>document.body.style.backgroundColor, []);\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isOpen && shouldScaleBackground) {\n            if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);\n            const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\") || document.querySelector(\"[vaul-drawer-wrapper]\");\n            if (!wrapper) return;\n            chain(setBackgroundColorOnScale && !noBodyStyles ? assignStyle(document.body, {\n                background: \"black\"\n            }) : noop, assignStyle(wrapper, {\n                transformOrigin: isVertical(direction) ? \"top\" : \"left\",\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            }));\n            const wrapperStylesCleanup = assignStyle(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`\n                }\n            });\n            return ()=>{\n                wrapperStylesCleanup();\n                timeoutIdRef.current = window.setTimeout(()=>{\n                    if (initialBackgroundColor) {\n                        document.body.style.background = initialBackgroundColor;\n                    } else {\n                        document.body.style.removeProperty(\"background\");\n                    }\n                }, TRANSITIONS.DURATION * 1000);\n            };\n        }\n    }, [\n        isOpen,\n        shouldScaleBackground,\n        initialBackgroundColor\n    ]);\n}\nlet previousBodyPosition = null;\n/**\n * This hook is necessary to prevent buggy behavior on iOS devices (need to test on Android).\n * I won't get into too much detail about what bugs it solves, but so far I've found that setting the body to `position: fixed` is the most reliable way to prevent those bugs.\n * Issues that this hook solves:\n * https://github.com/emilkowalski/vaul/issues/435\n * https://github.com/emilkowalski/vaul/issues/433\n * And more that I discovered, but were just not reported.\n */ function usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=> false ? 0 : \"\");\n    const scrollPos = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const setPositionFixed = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        // If previousBodyPosition is already set, don't set it again.\n        if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n            previousBodyPosition = {\n                position: document.body.style.position,\n                top: document.body.style.top,\n                left: document.body.style.left,\n                height: document.body.style.height,\n                right: \"unset\"\n            };\n            // Update the dom inside an animation frame\n            const { scrollX, innerHeight } = window;\n            document.body.style.setProperty(\"position\", \"fixed\", \"important\");\n            Object.assign(document.body.style, {\n                top: `${-scrollPos.current}px`,\n                left: `${-scrollX}px`,\n                right: \"0px\",\n                height: \"auto\"\n            });\n            window.setTimeout(()=>window.requestAnimationFrame(()=>{\n                    // Attempt to check if the bottom bar appeared due to the position change\n                    const bottomBarHeight = innerHeight - window.innerHeight;\n                    if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                        // Move the content further up so that the bottom bar doesn't hide it\n                        document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                    }\n                }), 300);\n        }\n    }, [\n        isOpen\n    ]);\n    const restorePositionSetting = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        if (previousBodyPosition !== null && !noBodyStyles) {\n            // Convert the position from \"px\" to Int\n            const y = -parseInt(document.body.style.top, 10);\n            const x = -parseInt(document.body.style.left, 10);\n            // Restore styles\n            Object.assign(document.body.style, previousBodyPosition);\n            window.requestAnimationFrame(()=>{\n                if (preventScrollRestoration && activeUrl !== window.location.href) {\n                    setActiveUrl(window.location.href);\n                    return;\n                }\n                window.scrollTo(x, y);\n            });\n            previousBodyPosition = null;\n        }\n    }, [\n        activeUrl\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        function onScroll() {\n            scrollPos.current = window.scrollY;\n        }\n        onScroll();\n        window.addEventListener(\"scroll\", onScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", onScroll);\n        };\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!modal) return;\n        return ()=>{\n            if (typeof document === \"undefined\") return;\n            // Another drawer is opened, safe to ignore the execution\n            const hasDrawerOpened = !!document.querySelector(\"[data-vaul-drawer]\");\n            if (hasDrawerOpened) return;\n            restorePositionSetting();\n        };\n    }, [\n        modal,\n        restorePositionSetting\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (nested || !hasBeenOpened) return;\n        // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n        if (isOpen) {\n            // avoid for standalone mode (PWA)\n            const isStandalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n            !isStandalone && setPositionFixed();\n            if (!modal) {\n                window.setTimeout(()=>{\n                    restorePositionSetting();\n                }, 500);\n            }\n        } else {\n            restorePositionSetting();\n        }\n    }, [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\nfunction Root({ open: openProp, onOpenChange, children, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, shouldScaleBackground = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, nested, noBodyStyles = false, direction = \"bottom\", defaultOpen = false, disablePreventScroll = true, snapToSequentialPoint = false, preventScrollRestoration = false, repositionInputs = true, onAnimationEnd, container, autoFocus = false }) {\n    var _drawerRef_current, _drawerRef_current1;\n    const [isOpen = false, setIsOpen] = useControllableState({\n        defaultProp: defaultOpen,\n        prop: openProp,\n        onChange: (o)=>{\n            onOpenChange == null ? void 0 : onOpenChange(o);\n            if (!o && !nested) {\n                restorePositionSetting();\n            }\n            setTimeout(()=>{\n                onAnimationEnd == null ? void 0 : onAnimationEnd(o);\n            }, TRANSITIONS.DURATION * 1000);\n            if (o && !modal) {\n                if (false) {}\n            }\n            if (!o) {\n                // This will be removed when the exit animation ends (`500ms`)\n                document.body.style.pointerEvents = \"auto\";\n            }\n        }\n    });\n    const [hasBeenOpened, setHasBeenOpened] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [justReleased, setJustReleased] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const overlayRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const openTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragEndTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastTimeDragPrevented = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isAllowedToDrag = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const nestedOpenChangeTimer = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const keyboardIsOpen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldAnimate = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!defaultOpen);\n    const previousDiffFromInitial = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const drawerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const drawerHeightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const drawerWidthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0);\n    const initialDrawerHeight = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const onSnapPointChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((activeSnapPointIndex)=>{\n        // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n        if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n    }, []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction,\n        container,\n        snapToSequentialPoint\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || !repositionInputs || !disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested: nested != null ? nested : false,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current, _drawerRef_current1;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        drawerWidthRef.current = ((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener(\"touchend\", ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.pageY : event.pageX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        // Fixes https://github.com/emilkowalski/vaul/issues/483\n        if (element.tagName === \"SELECT\") {\n            return false;\n        }\n        if (element.hasAttribute(\"data-vaul-no-drag\") || element.closest(\"[data-vaul-no-drag]\")) {\n            return false;\n        }\n        if (direction === \"right\" || direction === \"left\") {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === \"bottom\" ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (lastTimeDragPrevented.current && date.getTime() - lastTimeDragPrevented.current.getTime() < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute(\"role\") === \"dialog\") {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\");\n            const drawerDimension = direction === \"bottom\" || direction === \"top\" ? drawerHeightRef.current : drawerWidthRef.current;\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerDimension;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: \"none\"\n            });\n            set(overlayRef.current, {\n                transition: \"none\"\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        window.requestAnimationFrame(()=>{\n            shouldAnimate.current = true;\n        });\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _window_visualViewport;\n        function onVisualViewportChange() {\n            if (!drawerRef.current || !repositionInputs) return;\n            const focusedElement = document.activeElement;\n            if (isInput(focusedElement) || keyboardIsOpen.current) {\n                var _window_visualViewport;\n                const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                const totalHeight = window.innerHeight;\n                // This is the height of the keyboard\n                let diffFromInitial = totalHeight - visualViewportHeight;\n                const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                // Adjust drawer height only if it's tall enough\n                const isTallEnough = drawerHeight > totalHeight * 0.8;\n                if (!initialDrawerHeight.current) {\n                    initialDrawerHeight.current = drawerHeight;\n                }\n                const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                // visualViewport height may change due to somq e subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                    keyboardIsOpen.current = !keyboardIsOpen.current;\n                }\n                if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                    const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                    diffFromInitial += activeSnapPointHeight;\n                }\n                previousDiffFromInitial.current = diffFromInitial;\n                // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                    const height = drawerRef.current.getBoundingClientRect().height;\n                    let newDrawerHeight = height;\n                    if (height > visualViewportHeight) {\n                        newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : WINDOW_TOP_OFFSET);\n                    }\n                    // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                    if (fixed) {\n                        drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                    } else {\n                        drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                    }\n                } else if (!isMobileFirefox()) {\n                    drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                }\n                if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                    drawerRef.current.style.bottom = `0px`;\n                } else {\n                    // Negative bottom value would never make sense\n                    drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                }\n            }\n        }\n        (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener(\"resize\", onVisualViewportChange);\n        return ()=>{\n            var _window_visualViewport;\n            return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener(\"resize\", onVisualViewportChange);\n        };\n    }, [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer(fromWithin) {\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        if (!fromWithin) {\n            setIsOpen(false);\n        }\n        setTimeout(()=>{\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector(\"[data-vaul-drawer-wrapper]\");\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: \"translate3d(0, 0, 0)\",\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            opacity: \"1\"\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: \"top\"\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: \"left\"\n                },\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!event || !shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === \"bottom\" || direction === \"right\" ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        var _drawerRef_current_getBoundingClientRect_width;\n        const visibleDrawerWidth = Math.min((_drawerRef_current_getBoundingClientRect_width = drawerRef.current.getBoundingClientRect().width) != null ? _drawerRef_current_getBoundingClientRect_width : 0, window.innerWidth);\n        const isHorizontalSwipe = direction === \"left\" || direction === \"right\";\n        if (Math.abs(swipeAmount) >= (isHorizontalSwipe ? visibleDrawerWidth : visibleDrawerHeight) * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        if (isOpen) {\n            set(document.documentElement, {\n                scrollBehavior: \"auto\"\n            });\n            openTime.current = new Date();\n        }\n        return ()=>{\n            reset(document.documentElement, \"scrollBehavior\");\n        };\n    }, [\n        isOpen\n    ]);\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const initialTranslate = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${initialTranslate}px, 0)` : `scale(${scale}) translate3d(${initialTranslate}px, 0, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: \"none\",\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(_event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: \"none\"\n        });\n    }\n    function onNestedRelease(_event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!modal) {\n            // Need to do this manually unfortunately\n            window.requestAnimationFrame(()=>{\n                document.body.style.pointerEvents = \"auto\";\n            });\n        }\n    }, [\n        modal\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Root, {\n        defaultOpen: defaultOpen,\n        onOpenChange: (open)=>{\n            if (!dismissible && !open) return;\n            if (open) {\n                setHasBeenOpened(true);\n            } else {\n                closeDrawer(true);\n            }\n            setIsOpen(open);\n        },\n        open: isOpen\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DrawerContext.Provider, {\n        value: {\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            onOpenChange,\n            onPress,\n            onRelease,\n            onDrag,\n            dismissible,\n            shouldAnimate,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            modal,\n            snapPointsOffset,\n            activeSnapPointIndex,\n            direction,\n            shouldScaleBackground,\n            setBackgroundColorOnScale,\n            noBodyStyles,\n            container,\n            autoFocus\n        }\n    }, children));\n}\nconst Overlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, modal, shouldAnimate } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    // Overlay is the component that is locking scroll, removing it will unlock the scroll without having to dig into Radix's Dialog library\n    if (!modal) {\n        return null;\n    }\n    const onMouseUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>onRelease(event), [\n        onRelease\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Overlay, {\n        onMouseUp: onMouseUp,\n        ref: composedRef,\n        \"data-vaul-overlay\": \"\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-snap-points-overlay\": isOpen && shouldFade ? \"true\" : \"false\",\n        \"data-vaul-animate\": (shouldAnimate == null ? void 0 : shouldAnimate.current) ? \"true\" : \"false\",\n        ...rest\n    });\n});\nOverlay.displayName = \"Drawer.Overlay\";\nconst Content = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ onPointerDownOutside, style, onOpenAutoFocus, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, keyboardIsOpen, snapPointsOffset, activeSnapPointIndex, modal, isOpen, direction, snapPoints, container, handleOnly, shouldAnimate, autoFocus } = useDrawerContext();\n    // Needed to use transition instead of animations\n    const [delayedSnapPoints, setDelayedSnapPoints] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastKnownPointerEventRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const wasBeyondThePointRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    useScaleBackground();\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            \"bottom\",\n            \"right\"\n        ].includes(direction) ? 1 : -1;\n        if (direction === \"left\" || direction === \"right\") {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (hasSnapPoints) {\n            window.requestAnimationFrame(()=>{\n                setDelayedSnapPoints(true);\n            });\n        }\n    }, []);\n    function handleOnPointerUp(event) {\n        pointerStartRef.current = null;\n        wasBeyondThePointRef.current = false;\n        onRelease(event);\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Content, {\n        \"data-vaul-drawer-direction\": direction,\n        \"data-vaul-drawer\": \"\",\n        \"data-vaul-delayed-snap-points\": delayedSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? \"true\" : \"false\",\n        \"data-vaul-custom-container\": container ? \"true\" : \"false\",\n        \"data-vaul-animate\": (shouldAnimate == null ? void 0 : shouldAnimate.current) ? \"true\" : \"false\",\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            \"--snap-point-height\": `${snapPointsOffset[activeSnapPointIndex != null ? activeSnapPointIndex : 0]}px`,\n            ...style\n        } : style,\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.pageX,\n                y: event.pageY\n            };\n            onPress(event);\n        },\n        onOpenAutoFocus: (e)=>{\n            onOpenAutoFocus == null ? void 0 : onOpenAutoFocus(e);\n            if (!autoFocus) {\n                e.preventDefault();\n            }\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            lastKnownPointerEventRef.current = event;\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.pageY - pointerStartRef.current.y;\n            const xPosition = event.pageX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === \"touch\" ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        },\n        onPointerOut: (event)=>{\n            rest.onPointerOut == null ? void 0 : rest.onPointerOut.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        },\n        onContextMenu: (event)=>{\n            rest.onContextMenu == null ? void 0 : rest.onContextMenu.call(rest, event);\n            if (lastKnownPointerEventRef.current) {\n                handleOnPointerUp(lastKnownPointerEventRef.current);\n            }\n        }\n    });\n});\nContent.displayName = \"Drawer.Content\";\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, isOpen, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const shouldCancelInteractionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if (!snapPoints || snapPoints.length === 0) {\n            if (!dismissible) {\n                closeDrawer();\n            }\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        if (closeTimeoutIdRef.current) {\n            window.clearTimeout(closeTimeoutIdRef.current);\n        }\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"data-vaul-drawer-visible\": isOpen ? \"true\" : \"false\",\n        \"data-vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        \"data-vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = \"Drawer.Handle\";\nfunction NestedRoot({ onDrag, onOpenChange, open: nestedIsOpen, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error(\"Drawer.NestedRoot must be placed in another drawer\");\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Root, {\n        nested: true,\n        open: nestedIsOpen,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n            onOpenChange == null ? void 0 : onOpenChange(o);\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nfunction Portal(props) {\n    const context = useDrawerContext();\n    const { container = context.container, ...portalProps } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Portal, {\n        container: container,\n        ...portalProps\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Overlay,\n    Trigger: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Trigger,\n    Portal,\n    Handle,\n    Close: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Close,\n    Title: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Title,\n    Description: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Description\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vaul/dist/index.mjs\n");

/***/ })

};
;