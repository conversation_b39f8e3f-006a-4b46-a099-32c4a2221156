'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Sparkles, X } from 'lucide-react';
import { ChapterFilter } from '@/hooks/useChapterManagement';
import ChapterStatusFilter from './ChapterStatusFilter';
import { 
  EnhancedFilterControls, 
  EnhancedActionButtons, 
  EnhancedStatsDisplay 
} from '@/components/ui/EnhancedUI';
import { motion } from 'framer-motion';

interface ChapterListHeaderProps {
  stats: {
    total: number;
    scraped: number;
    enhanced: number;
    unscraped: number;
    scrapedNotEnhanced: number;
    selected: number;
  };
  selectionMode: 'scraping' | 'enhancement' | null;
  setSelectionMode: (mode: 'scraping' | 'enhancement' | null) => void;
  onFilterChange: (filter: ChapterFilter) => void;
  filter: ChapterFilter;
}

export const ChapterListHeader: React.FC<ChapterListHeaderProps> = ({ 
  stats,
  selectionMode, 
  setSelectionMode,
  onFilterChange,
  filter
}) => {
  const handleToggleScrapeMode = () => {
    setSelectionMode(selectionMode === 'scraping' ? null : 'scraping');
  };

  const handleToggleEnhanceMode = () => {
    setSelectionMode(selectionMode === 'enhancement' ? null : 'enhancement');
  };

  // Enhanced filter options with counts
  const enhancedFilters = {
    scraped: [
      { value: 'all', label: 'All', count: stats.total },
      { value: 'true', label: 'Scraped', count: stats.scraped },
      { value: 'false', label: 'Not Scraped', count: stats.unscraped }
    ],
    enhanced: [
      { value: 'all', label: 'All', count: stats.total },
      { value: 'true', label: 'Enhanced', count: stats.enhanced },
      { value: 'false', label: 'Not Enhanced', count: stats.total - stats.enhanced }
    ]
  };

  const enhancedValues = {
    scraped: filter.scraped === null ? 'all' : filter.scraped ? 'true' : 'false',
    enhanced: filter.enhanced === null ? 'all' : filter.enhanced ? 'true' : 'false'
  };

  const handleEnhancedFilterChange = (type: 'scraped' | 'enhanced', value: string) => {
    const newFilter = { ...filter };
    if (type === 'scraped') {
      newFilter.scraped = value === 'all' ? null : value === 'true';
    } else {
      newFilter.enhanced = value === 'all' ? null : value === 'true';
    }
    onFilterChange(newFilter);
  };

  const handleClearFilters = () => {
    onFilterChange({ scraped: null, enhanced: null });
  };

  return (
    <motion.div 
      className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50 mx-mobile-padding sm:mx-0"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Stats Display */}
      <div className="mb-4">
        <EnhancedStatsDisplay 
          stats={{
            total: stats.total,
            scraped: stats.scraped,
            enhanced: stats.enhanced,
            selected: stats.selected
          }}
        />
      </div>

      <div className="flex flex-col lg:flex-row items-stretch lg:items-center justify-between gap-4">
        {/* Enhanced Filter Section */}
        <div className="flex-1 max-w-2xl">
          <EnhancedFilterControls
            title="Filter Chapters"
            filters={enhancedFilters}
            values={enhancedValues}
            onChange={handleEnhancedFilterChange}
            onClear={handleClearFilters}
            compact={false}
          />
        </div>

        {/* Enhanced Action Buttons */}
        <div className="flex-shrink-0">
          <EnhancedActionButtons
            selectionMode={selectionMode}
            onToggleScrapeMode={handleToggleScrapeMode}
            onToggleEnhanceMode={handleToggleEnhanceMode}
            selectedCount={stats.selected}
            scrapableCount={stats.unscraped}
            enhancableCount={stats.scrapedNotEnhanced}
          />
        </div>
      </div>
    </motion.div>
  );
};
