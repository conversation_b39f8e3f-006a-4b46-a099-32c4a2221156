/**
 * RefreshButton Component
 * A reusable refresh button component with loading states and accessibility features
 * Built on top of Shadcn UI Button component
 */

import * as React from 'react';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { RefreshButtonProps } from '@/types/toast';
import { RefreshCw } from 'lucide-react';

interface ExtendedRefreshButtonProps extends RefreshButtonProps {
  /** Button variant from Shadcn UI */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  /** Button size from Shadcn UI */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Whether to show text alongside icon */
  showText?: boolean;
  /** Custom text to display */
  text?: string;
}

const RefreshButton = React.forwardRef<
  HTMLButtonElement,
  ExtendedRefreshButtonProps
>((
  {
    onRefresh,
    loading = false,
    disabled = false,
    className,
    ariaLabel = 'Refresh',
    variant = 'ghost',
    size = 'icon',
    showText = false,
    text = 'Refresh',
    ...props
  },
  ref
) => {
  const handleClick = React.useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      event.stopPropagation();
      
      if (!loading && !disabled) {
        onRefresh();
      }
    },
    [onRefresh, loading, disabled]
  );

  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLButtonElement>) => {
      // Handle Enter and Space keys for accessibility
      if ((event.key === 'Enter' || event.key === ' ') && !loading && !disabled) {
        event.preventDefault();
        event.stopPropagation();
        onRefresh();
      }
    },
    [onRefresh, loading, disabled]
  );

  const isDisabled = loading || disabled;

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={isDisabled}
      aria-label={loading ? `${ariaLabel} - Currently refreshing` : ariaLabel}
      aria-busy={loading}
      aria-disabled={isDisabled}
      aria-describedby={loading ? 'refresh-status' : undefined}
      role="button"
      tabIndex={isDisabled ? -1 : 0}
      className={cn(
        'transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500',
        'hover:scale-105 active:scale-95',
        isDisabled && 'cursor-not-allowed opacity-50',
        className
      )}
      {...props}
    >
      <RefreshCw 
        className={cn(
          'h-4 w-4',
          loading && 'animate-spin',
          showText && 'mr-2'
        )}
        aria-hidden="true"
      />
      {showText && (
        <span className="text-sm font-medium">
          {loading ? 'Refreshing...' : text}
        </span>
      )}
      {/* Hidden status for screen readers */}
      {loading && (
        <span 
          id="refresh-status" 
          className="sr-only" 
          aria-live="polite" 
          aria-atomic="true"
        >
          Refreshing content, please wait...
        </span>
      )}
    </Button>
  );
});

RefreshButton.displayName = 'RefreshButton';

export { RefreshButton, type ExtendedRefreshButtonProps };