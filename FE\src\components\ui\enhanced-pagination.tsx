'use client';

import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from '@/components/ui/pagination';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface EnhancedPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  loading?: boolean;
  scrollToTopOnChange?: boolean;
  scrollTargetRef?: React.RefObject<HTMLElement>;
  className?: string;
  showPageNumbers?: boolean;
  maxVisiblePages?: number;
}

const EnhancedPagination: React.FC<EnhancedPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  loading = false,
  scrollToTopOnChange = true,
  scrollTargetRef,
  className,
  showPageNumbers = true,
  maxVisiblePages = 5
}) => {
  const paginationRef = useRef<HTMLElement>(null);

  // Scroll to top function
  const scrollToTop = () => {
    if (scrollTargetRef?.current) {
      // Scroll to specific element
      scrollTargetRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    } else {
      // Scroll to top of page
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // Handle page change with loading state and scroll
  const handlePageChange = async (page: number) => {
    if (loading || page === currentPage || page < 1 || page > totalPages) {
      return;
    }

    // Trigger page change
    onPageChange(page);

    // Scroll to top if enabled
    if (scrollToTopOnChange) {
      // Small delay to ensure content starts loading
      setTimeout(() => {
        scrollToTop();
      }, 100);
    }
  };

  // Calculate visible page numbers
  const getVisiblePages = () => {
    if (!showPageNumbers) return [];
    
    const pages: number[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // Adjust start if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  if (totalPages <= 1) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn('relative', className)}
    >
      {/* Loading Overlay */}
      <AnimatePresence>
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg"
          >
            <div className="flex items-center gap-3 text-sm text-muted-foreground bg-background/90 px-4 py-2 rounded-lg shadow-lg border border-border/50">
              <div className="relative">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <div className="absolute inset-0 h-5 w-5 border-2 border-primary/20 rounded-full animate-ping" />
              </div>
              <span className="font-medium animate-pulse">Loading...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Pagination ref={paginationRef} className={cn(loading && 'pointer-events-none opacity-50')}>
        <PaginationContent>
          {/* Previous Button */}
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(currentPage - 1);
                }}
                className={cn(
                  'transition-all duration-200',
                  loading && 'cursor-not-allowed opacity-50'
                )}
              />
            </PaginationItem>
          )}

          {/* First page + ellipsis */}
          {showPageNumbers && visiblePages.length > 0 && visiblePages[0] > 1 && (
            <>
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(1);
                  }}
                  className={cn(
                    'transition-all duration-200',
                    loading && 'cursor-not-allowed opacity-50'
                  )}
                >
                  1
                </PaginationLink>
              </PaginationItem>
              {visiblePages[0] > 2 && (
                <PaginationItem>
                  <span className="px-2 text-muted-foreground">...</span>
                </PaginationItem>
              )}
            </>
          )}

          {/* Visible page numbers */}
          {showPageNumbers && visiblePages.map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(page);
                }}
                isActive={currentPage === page}
                className={cn(
                  'transition-all duration-200',
                  loading && 'cursor-not-allowed opacity-50',
                  currentPage === page && 'bg-primary text-primary-foreground'
                )}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}

          {/* Last page + ellipsis */}
          {showPageNumbers && visiblePages.length > 0 && visiblePages[visiblePages.length - 1] < totalPages && (
            <>
              {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
                <PaginationItem>
                  <span className="px-2 text-muted-foreground">...</span>
                </PaginationItem>
              )}
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(totalPages);
                  }}
                  className={cn(
                    'transition-all duration-200',
                    loading && 'cursor-not-allowed opacity-50'
                  )}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            </>
          )}

          {/* Next Button */}
          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(currentPage + 1);
                }}
                className={cn(
                  'transition-all duration-200',
                  loading && 'cursor-not-allowed opacity-50'
                )}
              />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>

      {/* Page Info */}
      <div className="mt-2 text-center text-sm text-muted-foreground">
        Page {currentPage} of {totalPages}
      </div>
    </motion.div>
  );
};

export default EnhancedPagination;