"use client"

import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FONT_FAMILIES } from '@/hooks/useReaderSettings';
import { BookOpen } from 'lucide-react';

type FontFamily = 'inter' | 'georgia' | 'times' | 'merriweather' | 'source-serif' | 'noto-serif' | 'roboto' | 'jetbrains-mono';

interface FontFamilyControlProps {
  fontFamily: FontFamily;
  onFontFamilyChange: (fontFamily: FontFamily) => void;
}

const FontFamilyControl: React.FC<FontFamilyControlProps> = ({
  fontFamily,
  onFontFamilyChange
}) => {
  return (
    <div className="flex flex-col space-y-3">
      <div className="flex items-center space-x-2">
        <BookOpen className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Font Family:</span>
        <Badge variant="secondary" className="text-xs">
          {FONT_FAMILIES[fontFamily].displayName}
        </Badge>
      </div>
      
      <Select value={fontFamily} onValueChange={(newFont: FontFamily) => {
        // DEBUG LOGS - Validate font selection
        console.log('🎯 FontFamilyControl Debug:', {
          oldFont: fontFamily,
          newFont,
          fontData: FONT_FAMILIES[newFont],
          timestamp: new Date().toISOString()
        });
        onFontFamilyChange(newFont);
      }}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select font family" />
        </SelectTrigger>
        <SelectContent className="max-w-[300px] sm:max-w-[350px] lg:max-w-[400px]">
          {Object.entries(FONT_FAMILIES).map(([key, font]) => (
            <SelectItem key={key} value={key} className="py-3 px-3">
              <div className="flex flex-col">
                <span
                  className="font-medium text-sm"
                  style={{ fontFamily: font.cssFamily }}
                >
                  {font.displayName}
                </span>
                <span className="text-xs text-muted-foreground hidden sm:block mt-0.5">
                  {font.description}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default FontFamilyControl;