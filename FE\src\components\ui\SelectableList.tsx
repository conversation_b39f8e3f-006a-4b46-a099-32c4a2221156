'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Context for selectable list
interface SelectableListContextValue {
  selectionMode: string | null;
  selectedItems: Set<string>;
  onSelectItem: (id: string) => void;
  isItemSelectable?: (item: any) => boolean;
}

const SelectableListContext = React.createContext<SelectableListContextValue>({
  selectionMode: null,
  selectedItems: new Set(),
  onSelectItem: () => {},
  isItemSelectable: () => true
});

// Root component
interface SelectableListRootProps {
  children: React.ReactNode;
  selectionMode: string | null;
  selectedItems: Set<string>;
  onSelectItem: (id: string) => void;
  isItemSelectable?: (item: any) => boolean;
  className?: string;
}

const SelectableListRoot: React.FC<SelectableListRootProps> = ({
  children,
  selectionMode,
  selectedItems,
  onSelectItem,
  isItemSelectable = () => true,
  className
}) => {
  const contextValue = {
    selectionMode,
    selectedItems,
    onSelectItem,
    isItemSelectable
  };

  return (
    <SelectableListContext.Provider value={contextValue}>
      <div className={cn('space-y-2', className)}>
        {children}
      </div>
    </SelectableListContext.Provider>
  );
};

// Header with selection controls
interface SelectableListHeaderProps {
  title: string;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  className?: string;
}

const SelectableListHeader: React.FC<SelectableListHeaderProps> = ({
  title,
  onSelectAll,
  onDeselectAll,
  className
}) => {
  const { selectionMode } = React.useContext(SelectableListContext);

  if (!selectionMode) return null;

  return (
    <div className={cn(
      'flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-gray-100 dark:bg-gray-800 rounded-t-lg gap-3 sm:gap-4',
      className
    )}>
      <p className="text-sm font-medium">{title}</p>
      <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
        <Button 
          size="sm" 
          variant="outline" 
          onClick={onSelectAll}
          className="w-full sm:w-auto justify-center"
        >
          Select All Visible
        </Button>
        <Button 
          size="sm" 
          variant="outline" 
          onClick={onDeselectAll}
          className="w-full sm:w-auto justify-center"
        >
          Clear Selection
        </Button>
      </div>
    </div>
  );
};

// Container for list items
interface SelectableListContainerProps {
  children: React.ReactNode;
  loading?: boolean;
  emptyMessage?: string;
  layout?: 'grid' | 'list';
  gridCols?: 1 | 2 | 3 | 4;
  className?: string;
}

const SelectableListContainer: React.FC<SelectableListContainerProps> = ({
  children,
  loading = false,
  emptyMessage = 'No items found.',
  layout = 'grid',
  gridCols = 2,
  className
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4 sm:p-8">
        <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (React.Children.count(children) === 0) {
    return (
      <p className="text-center p-4 sm:p-8 text-gray-500 text-sm sm:text-base">{emptyMessage}</p>
    );
  }

  const layoutClasses = {
    grid: {
      1: 'grid grid-cols-1 gap-2 sm:gap-3',
      2: 'grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3',
      3: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3',
      4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-3'
    },
    list: {
      1: 'space-y-2 sm:space-y-3',
      2: 'space-y-2 sm:space-y-3',
      3: 'space-y-2 sm:space-y-3',
      4: 'space-y-2 sm:space-y-3'
    }
  };

  return (
    <div className={cn(
      layoutClasses[layout][gridCols],
      className
    )}>
      {children}
    </div>
  );
};

// Individual selectable item
interface SelectableListItemProps {
  id: string;
  children: React.ReactNode;
  item?: any; // For isItemSelectable check
  className?: string;
  disabled?: boolean;
}

const SelectableListItem: React.FC<SelectableListItemProps> = ({
  id,
  children,
  item,
  className,
  disabled = false
}) => {
  const { selectionMode, selectedItems, onSelectItem, isItemSelectable } = React.useContext(SelectableListContext);
  
  const isSelected = selectedItems.has(id);
  const isSelectable = !disabled && (!item || isItemSelectable?.(item));

  return (
    <div className={cn(
      'flex items-center p-2.5 sm:p-3 rounded-lg transition-colors min-h-[3rem] sm:min-h-[3.5rem] touch-manipulation',
      {
        'bg-indigo-50 dark:bg-indigo-900/20': isSelected,
        'hover:bg-gray-50 dark:hover:bg-gray-800/50 active:bg-gray-100 dark:active:bg-gray-800': !isSelected
      },
      className
    )}>
      {selectionMode && (
        <div className="mr-3 sm:mr-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelectItem(id)}
            disabled={!isSelectable}
            aria-label={`Select item ${id}`}
            className="h-4 w-4 sm:h-5 sm:w-5"
          />
        </div>
      )}
      <div className="flex-grow min-w-0">
        {children}
      </div>
    </div>
  );
};

// Content wrapper for item content
interface SelectableListItemContentProps {
  children: React.ReactNode;
  href?: string;
  onClick?: () => void;
  className?: string;
}

const SelectableListItemContent: React.FC<SelectableListItemContentProps> = ({
  children,
  href,
  onClick,
  className
}) => {
  const baseClasses = 'flex items-center gap-4 cursor-pointer w-full';
  
  if (href) {
    const Link = require('next/link').default;
    return (
      <Link href={href} className={cn(baseClasses, className)}>
        {children}
      </Link>
    );
  }
  
  if (onClick) {
    return (
      <button onClick={onClick} className={cn(baseClasses, className)}>
        {children}
      </button>
    );
  }
  
  return (
    <div className={cn('flex items-center gap-4', className)}>
      {children}
    </div>
  );
};

// Stats component for showing selection info
interface SelectableListStatsProps {
  totalItems: number;
  selectedCount: number;
  className?: string;
}

const SelectableListStats: React.FC<SelectableListStatsProps> = ({
  totalItems,
  selectedCount,
  className
}) => {
  const { selectionMode } = React.useContext(SelectableListContext);
  
  if (!selectionMode) return null;
  
  return (
    <div className={cn('text-sm text-muted-foreground p-2', className)}>
      {selectedCount} of {totalItems} items selected
    </div>
  );
};

// Export compound component
export const SelectableList = {
  Root: SelectableListRoot,
  Header: SelectableListHeader,
  Container: SelectableListContainer,
  Item: SelectableListItem,
  ItemContent: SelectableListItemContent,
  Stats: SelectableListStats
};

export default SelectableList;