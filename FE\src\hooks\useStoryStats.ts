import { useState, useEffect } from 'react';
import { Story } from '@/types/story';
import { storiesService } from '@/services/storyService';
import { createComponentError, handleError, safeAsync, getUserFriendlyMessage, normalizeError } from '@/utils/errorHandling';
import { ErrorLike } from '@/types/errors';

interface StoryStats {
  total_chapters: number;
  total_chapters_scraped: number;
  total_chapters_enhanced: number;
  loading: boolean;
  error: string | null;
}

/**
 * Hook to fetch and manage story statistics
 * This ensures we get the most up-to-date chapter counts
 */
export const useStoryStats = (storyId: string, initialStory?: Story): StoryStats & { refreshStats: () => void } => {
  const [stats, setStats] = useState<StoryStats>({
    total_chapters: initialStory?.total_chapters || 0,
    total_chapters_scraped: initialStory?.total_chapters_scraped || 0,
    total_chapters_enhanced: initialStory?.total_chapters_enhanced || 0,
    loading: false,
    error: null
  });

  const fetchStoryStats = async () => {
    if (!storyId) return;

    setStats(prev => ({ ...prev, loading: true, error: null }));

    const [error, storyData] = await safeAsync(async () => {
      return await storiesService.getStoryById(storyId);
    });

    if (error) {
      const componentError = createComponentError(
        error,
        'useStoryStats',
        'fetchStoryStats'
      );
      handleError(componentError);
      const errorMessage = getUserFriendlyMessage(normalizeError(error as ErrorLike));
      setStats(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
      return;
    }

    if (storyData) {
      setStats({
        total_chapters: storyData.total_chapters || 0,
        total_chapters_scraped: storyData.total_chapters_scraped || 0,
        total_chapters_enhanced: storyData.total_chapters_enhanced || 0,
        loading: false,
        error: null
      });
    } else {
      setStats(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to fetch story data'
      }));
    }
  };

  useEffect(() => {
    fetchStoryStats();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storyId]);

  // Refresh stats function that can be called externally
  const refreshStats = () => {
    fetchStoryStats();
  };

  return {
    ...stats,
    refreshStats
  };
};