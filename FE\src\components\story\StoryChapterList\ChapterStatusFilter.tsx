'use client';

import React from 'react';

export interface ChapterFilter {
  scraped?: boolean | null; // null = all, true = scraped, false = not scraped
  enhanced?: boolean | null; // null = all, true = enhanced, false = not enhanced
}

interface ChapterStatusFilterProps {
  filter: ChapterFilter;
  onFilterChange: (filter: ChapterFilter) => void;
  className?: string;
}

const ChapterStatusFilter: React.FC<ChapterStatusFilterProps> = ({
  filter,
  onFilterChange,
  className = ''
}) => {
  const handleScrapedFilterChange = (value: string) => {
    const scraped = value === 'all' ? null : value === 'true';
    onFilterChange({ ...filter, scraped });
  };

  const handleEnhancedFilterChange = (value: string) => {
    const enhanced = value === 'all' ? null : value === 'true';
    onFilterChange({ ...filter, enhanced });
  };

  const getScrapedValue = () => {
    if (filter.scraped === null) return 'all';
    return filter.scraped ? 'true' : 'false';
  };

  const getEnhancedValue = () => {
    if (filter.enhanced === null) return 'all';
    return filter.enhanced ? 'true' : 'false';
  };

  return (
    <div className={`bg-zinc-800/50 rounded-lg p-2.5 xs:p-3 sm:p-4 border border-zinc-700/50 ${className}`}>
      {/* Mobile-first layout */}
      <div className="space-y-3 xs:space-y-0">
        {/* Header - hidden on mobile to save space */}
        <h3 className="hidden xs:block text-xs sm:text-sm font-medium text-gray-300 mb-2">Filter:</h3>
        
        {/* Filter Controls */}
        <div className="flex flex-col xs:flex-row items-stretch xs:items-center gap-2 xs:gap-3 sm:gap-4">
          {/* Scraped Status Filter */}
          <div className="flex items-center justify-between xs:justify-start gap-2">
            <label className="text-xs xs:text-sm text-muted-foreground min-w-0 flex-shrink-0">Scrape:</label>
            <select
              value={getScrapedValue()}
              onChange={(e) => handleScrapedFilterChange(e.target.value)}
              className="px-2 xs:px-3 py-1.5 xs:py-1 text-xs xs:text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation min-h-touch-target xs:min-h-0 flex-1 xs:flex-none"
              aria-label="Filter by scrape status"
            >
              <option value="all">All</option>
              <option value="true">Scraped</option>
              <option value="false">Not Scraped</option>
            </select>
          </div>

          {/* Enhanced Status Filter */}
          <div className="flex items-center justify-between xs:justify-start gap-2">
            <label className="text-xs xs:text-sm text-muted-foreground min-w-0 flex-shrink-0">Enhance:</label>
            <select
              value={getEnhancedValue()}
              onChange={(e) => handleEnhancedFilterChange(e.target.value)}
              className="px-2 xs:px-3 py-1.5 xs:py-1 text-xs xs:text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation min-h-touch-target xs:min-h-0 flex-1 xs:flex-none"
              aria-label="Filter by enhancement status"
            >
              <option value="all">All</option>
              <option value="true">Enhanced</option>
              <option value="false">Not Enhanced</option>
            </select>
          </div>

          {/* Clear Filters Button */}
          <button
            onClick={() => onFilterChange({ scraped: null, enhanced: null })}
            className="px-3 xs:px-3 py-1.5 xs:py-1 text-xs xs:text-sm bg-zinc-600 hover:bg-zinc-500 active:bg-zinc-400 text-white rounded transition-colors touch-manipulation min-h-touch-target xs:min-h-0 w-full xs:w-auto"
            aria-label="Clear all filters"
          >
            <span className="xs:hidden">Clear All Filters</span>
            <span className="hidden xs:inline">Clear</span>
          </button>
        </div>
      </div>

      {/* Active Filters Display */}
      {(filter.scraped !== null || filter.enhanced !== null) && (
        <div className="mt-2 xs:mt-3 flex flex-wrap gap-1.5 xs:gap-2">
          {filter.scraped !== null && (
            <span className="inline-flex items-center px-2 py-1 text-xs bg-green-900/30 text-green-400 rounded touch-manipulation">
              <span className="xs:hidden">Scraped: {filter.scraped ? 'Yes' : 'No'}</span>
              <span className="hidden xs:inline">Scraped: {filter.scraped ? 'Yes' : 'No'}</span>
              <button
                onClick={() => onFilterChange({ ...filter, scraped: null })}
                className="ml-1.5 text-green-300 hover:text-green-200 active:text-green-100 w-4 h-4 flex items-center justify-center touch-manipulation"
                aria-label="Remove scrape filter"
              >
                X
              </button>
            </span>
          )}
          {filter.enhanced !== null && (
            <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-900/30 text-blue-400 rounded touch-manipulation">
              <span className="xs:hidden">Enhanced: {filter.enhanced ? 'Yes' : 'No'}</span>
              <span className="hidden xs:inline">Enhanced: {filter.enhanced ? 'Yes' : 'No'}</span>
              <button
                onClick={() => onFilterChange({ ...filter, enhanced: null })}
                className="ml-1.5 text-blue-300 hover:text-blue-200 active:text-blue-100 w-4 h-4 flex items-center justify-center touch-manipulation"
                aria-label="Remove enhancement filter"
              >
                X
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default ChapterStatusFilter;