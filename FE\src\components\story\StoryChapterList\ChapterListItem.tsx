'use client';

import React from 'react';
import Link from 'next/link';
import { Chapter } from '@/types/story';
import { Checkbox } from '@/components/ui/checkbox';
import { ChapterStatusBadges } from '@/components/ui/StatusBadge';
import { cleanChapterTitle } from '@/utils/chapterUtils';

interface ChapterListItemProps {
  storyId: string;
  chapter: Chapter;
  isSelected: boolean;
  selectionMode: 'scraping' | 'enhancement' | null;
  onSelectChapter: (chapterId: string) => void;
}

export const ChapterListItem: React.FC<ChapterListItemProps> = React.memo(({ 
  storyId, 
  chapter, 
  isSelected, 
  selectionMode, 
  onSelectChapter 
}) => {

  const isSelectable = 
    (selectionMode === 'scraping' && !chapter.is_scraped) ||
    (selectionMode === 'enhancement' && chapter.is_scraped && !chapter.is_enhanced);

  const handleItemClick = (e: React.MouseEvent) => {
    if (selectionMode) {
      e.preventDefault();
      if (isSelectable) {
        onSelectChapter(chapter.id);
      }
    }
  };

  return (
    <div 
      className={`flex items-center p-3 rounded-lg transition-colors ${isSelected ? 'bg-indigo-50 dark:bg-indigo-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'} ${selectionMode && isSelectable ? 'cursor-pointer' : ''}`}
      onClick={handleItemClick}
    >
      {selectionMode && (
        <div className="mr-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelectChapter(chapter.id)}
            disabled={!isSelectable}
            aria-label={`Select chapter ${chapter.chapter_number}`}
          />
        </div>
      )}
      <Link href={`/stories/${storyId}/${chapter.chapter_number}`} className="flex-grow flex items-center gap-4 cursor-pointer" onClick={(e) => selectionMode && e.preventDefault()}>
        <span className="flex-grow font-medium">
          {cleanChapterTitle(chapter.title)}
        </span>
        <ChapterStatusBadges
          isScraped={chapter.is_scraped}
          isEnhanced={chapter.is_enhanced}
          size="sm"
        />
      </Link>
    </div>
  );
});

ChapterListItem.displayName = 'ChapterListItem';
