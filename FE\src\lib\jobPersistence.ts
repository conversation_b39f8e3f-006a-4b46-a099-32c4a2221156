/**
 * Job Persistence Utilities
 * 
 * Manages scraping job persistence in localStorage with:
 * - Job metadata storage and retrieval
 * - Automatic cleanup of expired jobs
 * - Job status tracking
 * - Recovery mechanisms
 */

export interface PersistedJob {
  jobId: string;
  url: string;
  maxPages: number;
  timestamp: number;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  title?: string;
  progress?: {
    phase: string;
    percentage: number;
    currentStep: number;
    totalSteps: number;
  };
}

const STORAGE_KEY = 'scrapingJobs';
const JOB_EXPIRY_TIME = 30 * 60 * 1000; // 30 minutes

/**
 * Get all persisted jobs from localStorage
 */
export const getPersistedJobs = (): PersistedJob[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    
    const jobs: PersistedJob[] = JSON.parse(stored);
    return jobs.filter(job => {
      const isExpired = Date.now() - job.timestamp > JOB_EXPIRY_TIME;
      return !isExpired;
    });
  } catch (error) {
    console.error('Failed to get persisted jobs:', error);
    return [];
  }
};

/**
 * Get a specific job by ID
 */
export const getPersistedJob = (jobId: string): PersistedJob | null => {
  const jobs = getPersistedJobs();
  return jobs.find(job => job.jobId === jobId) || null;
};

/**
 * Save or update a job in localStorage
 */
export const persistJob = (job: PersistedJob): void => {
  try {
    const jobs = getPersistedJobs();
    const existingIndex = jobs.findIndex(j => j.jobId === job.jobId);
    
    if (existingIndex >= 0) {
      jobs[existingIndex] = job;
    } else {
      jobs.push(job);
    }
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(jobs));
  } catch (error) {
    console.error('Failed to persist job:', error);
  }
};

/**
 * Remove a job from localStorage
 */
export const removePersistedJob = (jobId: string): void => {
  try {
    const jobs = getPersistedJobs();
    const filteredJobs = jobs.filter(job => job.jobId !== jobId);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredJobs));
  } catch (error) {
    console.error('Failed to remove persisted job:', error);
  }
};

/**
 * Get all active jobs (not completed, failed, or cancelled)
 */
export const getActiveJobs = (): PersistedJob[] => {
  return getPersistedJobs().filter(job => job.status === 'active');
};

/**
 * Update job status
 */
export const updateJobStatus = (jobId: string, status: PersistedJob['status']): void => {
  const job = getPersistedJob(jobId);
  if (job) {
    persistJob({ ...job, status, timestamp: Date.now() });
  }
};

/**
 * Update job progress
 */
export const updateJobProgress = (jobId: string, progress: PersistedJob['progress']): void => {
  const job = getPersistedJob(jobId);
  if (job) {
    persistJob({ ...job, progress, timestamp: Date.now() });
  }
};

/**
 * Clean up expired and completed jobs
 */
export const cleanupJobs = (): void => {
  try {
    const jobs = getPersistedJobs();
    const activeJobs = jobs.filter(job => {
      const isExpired = Date.now() - job.timestamp > JOB_EXPIRY_TIME;
      const isCompleted = ['completed', 'failed', 'cancelled'].includes(job.status);
      
      // Keep active jobs that aren't expired, remove completed/failed jobs older than 5 minutes
      if (isCompleted) {
        return Date.now() - job.timestamp < 5 * 60 * 1000;
      }
      
      return !isExpired;
    });
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(activeJobs));
  } catch (error) {
    console.error('Failed to cleanup jobs:', error);
  }
};

/**
 * Check if there are any active jobs
 */
export const hasActiveJobs = (): boolean => {
  return getActiveJobs().length > 0;
};

/**
 * Legacy support - migrate old single job storage to new system
 */
export const migrateLegacyJob = (): void => {
  try {
    const legacyJob = localStorage.getItem('activeScrapingJob');
    if (legacyJob) {
      const jobData = JSON.parse(legacyJob);
      const migratedJob: PersistedJob = {
        jobId: jobData.jobId,
        url: jobData.url || '',
        maxPages: jobData.maxPages || 10,
        timestamp: jobData.timestamp || Date.now(),
        status: 'active'
      };
      
      persistJob(migratedJob);
      localStorage.removeItem('activeScrapingJob');
    }
  } catch (error) {
    console.error('Failed to migrate legacy job:', error);
  }
};