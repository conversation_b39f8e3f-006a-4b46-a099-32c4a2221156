'use client';

import React, { memo, useMemo, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Loader2 } from 'lucide-react';

interface BatchActionsPanelProps {
  selectionMode: 'scraping' | 'enhancement';
  selectedCount: number;
  isProcessing: boolean;
  progress: number;
  onStartAction: (chapterIds: string[]) => void;
  onCancel: () => void;
  selectedChapterIds: string[];
}

export const BatchActionsPanel: React.FC<BatchActionsPanelProps> = memo(({ 
  selectionMode, 
  selectedCount, 
  isProcessing, 
  progress, 
  onStartAction, 
  onCancel,
  selectedChapterIds
}) => {
  const actionText = useMemo(() => 
    selectionMode === 'scraping' ? 'Scrape' : 'Enhance',
    [selectionMode]
  );

  const handleStartAction = useCallback(() => {
    onStartAction(selectedChapterIds);
  }, [onStartAction, selectedChapterIds]);

  const progressText = useMemo(() => 
    `${Math.round(progress)}% complete`,
    [progress]
  );

  return (
    <div className="fixed bottom-0 left-0 right-0 sm:bottom-4 sm:right-4 sm:left-auto w-full sm:w-96 bg-white dark:bg-gray-800 border-t sm:border border-gray-200 dark:border-gray-700 sm:rounded-lg shadow-lg p-3 sm:p-4 z-50 safe-area-padding-bottom">
      {/* Header */}
      <div className="flex items-center justify-between mb-2 sm:mb-3">
        <div>
          <h3 className="font-bold text-base sm:text-lg text-gray-900 dark:text-white">Batch {actionText}</h3>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {selectedCount} {selectedCount === 1 ? 'chapter' : 'chapters'} selected
          </p>
        </div>
        {/* Mobile close button */}
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onCancel}
          className="sm:hidden p-1 h-8 w-8 touch-manipulation"
          aria-label="Close batch actions"
        >
          X
        </Button>
      </div>
      
      {/* Progress Section */}
      {isProcessing && (
        <div className="mb-3 sm:mb-4">
          <Progress value={progress} className="w-full h-2 sm:h-3" />
          <p className="text-xs text-center mt-1 text-gray-600 dark:text-gray-300">{progressText}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 sm:gap-2">
        <Button 
          variant="outline" 
          onClick={onCancel} 
          disabled={isProcessing}
          className="hidden sm:flex min-h-touch-target sm:min-h-0 touch-manipulation"
          size="sm"
        >
          Cancel
        </Button>
        <Button 
          onClick={handleStartAction} 
          disabled={isProcessing || selectedCount === 0}
          className="min-h-touch-target sm:min-h-0 touch-manipulation w-full sm:w-auto"
          size="sm"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 animate-spin mr-2" aria-hidden="true" />
              <span>{actionText}ing...</span>
            </>
          ) : (
            <span>Start {actionText}</span>
          )}
        </Button>
      </div>
    </div>
  );
});

BatchActionsPanel.displayName = 'BatchActionsPanel';
