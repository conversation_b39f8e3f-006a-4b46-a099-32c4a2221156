"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ $e),\n/* harmony export */   toast: () => (/* binding */ ue),\n/* harmony export */   useSonner: () => (/* binding */ Oe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar jt = (n)=>{\n    switch(n){\n        case \"success\":\n            return ee;\n        case \"info\":\n            return ae;\n        case \"warning\":\n            return oe;\n        case \"error\":\n            return se;\n        default:\n            return null;\n    }\n}, te = Array(12).fill(0), Yt = ({ visible: n, className: e })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            e\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": n\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, te.map((t, a)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${a}`\n        })))), ee = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), oe = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), ae = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), se = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n})), Ot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nvar Ft = ()=>{\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let t = ()=>{\n            e(document.hidden);\n        };\n        return document.addEventListener(\"visibilitychange\", t), ()=>window.removeEventListener(\"visibilitychange\", t);\n    }, []), n;\n};\n\nvar bt = 1, yt = class {\n    constructor(){\n        this.subscribe = (e)=>(this.subscribers.push(e), ()=>{\n                let t = this.subscribers.indexOf(e);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (e)=>{\n            this.subscribers.forEach((t)=>t(e));\n        };\n        this.addToast = (e)=>{\n            this.publish(e), this.toasts = [\n                ...this.toasts,\n                e\n            ];\n        };\n        this.create = (e)=>{\n            var S;\n            let { message: t, ...a } = e, u = typeof (e == null ? void 0 : e.id) == \"number\" || ((S = e.id) == null ? void 0 : S.length) > 0 ? e.id : bt++, f = this.toasts.find((g)=>g.id === u), w = e.dismissible === void 0 ? !0 : e.dismissible;\n            return this.dismissedToasts.has(u) && this.dismissedToasts.delete(u), f ? this.toasts = this.toasts.map((g)=>g.id === u ? (this.publish({\n                    ...g,\n                    ...e,\n                    id: u,\n                    title: t\n                }), {\n                    ...g,\n                    ...e,\n                    id: u,\n                    dismissible: w,\n                    title: t\n                }) : g) : this.addToast({\n                title: t,\n                ...a,\n                dismissible: w,\n                id: u\n            }), u;\n        };\n        this.dismiss = (e)=>(this.dismissedToasts.add(e), e || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((a)=>a({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: e,\n                    dismiss: !0\n                })), e);\n        this.message = (e, t)=>this.create({\n                ...t,\n                message: e\n            });\n        this.error = (e, t)=>this.create({\n                ...t,\n                message: e,\n                type: \"error\"\n            });\n        this.success = (e, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: e\n            });\n        this.info = (e, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: e\n            });\n        this.warning = (e, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: e\n            });\n        this.loading = (e, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: e\n            });\n        this.promise = (e, t)=>{\n            if (!t) return;\n            let a;\n            t.loading !== void 0 && (a = this.create({\n                ...t,\n                promise: e,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let u = e instanceof Promise ? e : e(), f = a !== void 0, w, S = u.then(async (i)=>{\n                if (w = [\n                    \"resolve\",\n                    i\n                ], /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(i)) f = !1, this.create({\n                    id: a,\n                    type: \"default\",\n                    message: i\n                });\n                else if (ie(i) && !i.ok) {\n                    f = !1;\n                    let T = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${i.status}`) : t.error, F = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${i.status}`) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: T,\n                        description: F\n                    });\n                } else if (t.success !== void 0) {\n                    f = !1;\n                    let T = typeof t.success == \"function\" ? await t.success(i) : t.success, F = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"success\",\n                        message: T,\n                        description: F\n                    });\n                }\n            }).catch(async (i)=>{\n                if (w = [\n                    \"reject\",\n                    i\n                ], t.error !== void 0) {\n                    f = !1;\n                    let D = typeof t.error == \"function\" ? await t.error(i) : t.error, T = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: D,\n                        description: T\n                    });\n                }\n            }).finally(()=>{\n                var i;\n                f && (this.dismiss(a), a = void 0), (i = t.finally) == null || i.call(t);\n            }), g = ()=>new Promise((i, D)=>S.then(()=>w[0] === \"reject\" ? D(w[1]) : i(w[1])).catch(D));\n            return typeof a != \"string\" && typeof a != \"number\" ? {\n                unwrap: g\n            } : Object.assign(a, {\n                unwrap: g\n            });\n        };\n        this.custom = (e, t)=>{\n            let a = (t == null ? void 0 : t.id) || bt++;\n            return this.create({\n                jsx: e(a),\n                id: a,\n                ...t\n            }), a;\n        };\n        this.getActiveToasts = ()=>this.toasts.filter((e)=>!this.dismissedToasts.has(e.id));\n        this.subscribers = [], this.toasts = [], this.dismissedToasts = new Set;\n    }\n}, v = new yt, ne = (n, e)=>{\n    let t = (e == null ? void 0 : e.id) || bt++;\n    return v.addToast({\n        title: n,\n        ...e,\n        id: t\n    }), t;\n}, ie = (n)=>n && typeof n == \"object\" && \"ok\" in n && typeof n.ok == \"boolean\" && \"status\" in n && typeof n.status == \"number\", le = ne, ce = ()=>v.toasts, de = ()=>v.getActiveToasts(), ue = Object.assign(le, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: ce,\n    getToasts: de\n});\nfunction wt(n, { insertAt: e } = {}) {\n    if (!n || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], a = document.createElement(\"style\");\n    a.type = \"text/css\", e === \"top\" && t.firstChild ? t.insertBefore(a, t.firstChild) : t.appendChild(a), a.styleSheet ? a.styleSheet.cssText = n : a.appendChild(document.createTextNode(n));\n}\nwt(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\"left\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction tt(n) {\n    return n.label !== void 0;\n}\nvar pe = 3, me = \"32px\", ge = \"16px\", Wt = 4e3, he = 356, be = 14, ye = 20, we = 200;\nfunction M(...n) {\n    return n.filter(Boolean).join(\" \");\n}\nfunction xe(n) {\n    let [e, t] = n.split(\"-\"), a = [];\n    return e && a.push(e), t && a.push(t), a;\n}\nvar ve = (n)=>{\n    var Dt, Pt, Nt, Bt, Ct, kt, It, Mt, Ht, At, Lt;\n    let { invert: e, toast: t, unstyled: a, interacting: u, setHeights: f, visibleToasts: w, heights: S, index: g, toasts: i, expanded: D, removeToast: T, defaultRichColors: F, closeButton: et, style: ut, cancelButtonStyle: ft, actionButtonStyle: l, className: ot = \"\", descriptionClassName: at = \"\", duration: X, position: st, gap: pt, loadingIcon: rt, expandByDefault: B, classNames: s, icons: P, closeButtonAriaLabel: nt = \"Close toast\", pauseWhenPageIsHidden: it } = n, [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [A, mt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [L, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [ct, d] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [h, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [R, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [p, _] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), O = react__WEBPACK_IMPORTED_MODULE_0__.useRef(t.duration || X || Wt), G = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), k = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Vt = g === 0, Ut = g + 1 <= w, N = t.type, V = t.dismissible !== !1, Kt = t.className || \"\", Xt = t.descriptionClassName || \"\", dt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>S.findIndex((r)=>r.toastId === t.id) || 0, [\n        S,\n        t.id\n    ]), Jt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var r;\n        return (r = t.closeButton) != null ? r : et;\n    }, [\n        t.closeButton,\n        et\n    ]), Tt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>t.duration || X || Wt, [\n        t.duration,\n        X\n    ]), gt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), U = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), St = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), K = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [Gt, Qt] = st.split(\"-\"), Rt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>S.reduce((r, m, c)=>c >= dt ? r : r + m.height, 0), [\n        S,\n        dt\n    ]), Et = Ft(), qt = t.invert || e, ht = N === \"loading\";\n    U.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>dt * pt + Rt, [\n        dt,\n        Rt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        O.current = Tt;\n    }, [\n        Tt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        H(!0);\n    }, []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let r = k.current;\n        if (r) {\n            let m = r.getBoundingClientRect().height;\n            return _(m), f((c)=>[\n                    {\n                        toastId: t.id,\n                        height: m,\n                        position: t.position\n                    },\n                    ...c\n                ]), ()=>f((c)=>c.filter((b)=>b.toastId !== t.id));\n        }\n    }, [\n        f,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        if (!W) return;\n        let r = k.current, m = r.style.height;\n        r.style.height = \"auto\";\n        let c = r.getBoundingClientRect().height;\n        r.style.height = m, _(c), f((b)=>b.find((x)=>x.toastId === t.id) ? b.map((x)=>x.toastId === t.id ? {\n                    ...x,\n                    height: c\n                } : x) : [\n                {\n                    toastId: t.id,\n                    height: c,\n                    position: t.position\n                },\n                ...b\n            ]);\n    }, [\n        W,\n        t.title,\n        t.description,\n        f,\n        t.id\n    ]);\n    let $ = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        mt(!0), j(U.current), f((r)=>r.filter((m)=>m.toastId !== t.id)), setTimeout(()=>{\n            T(t);\n        }, we);\n    }, [\n        t,\n        T,\n        f,\n        U\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (t.promise && N === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n        let r;\n        return D || u || it && Et ? (()=>{\n            if (St.current < gt.current) {\n                let b = new Date().getTime() - gt.current;\n                O.current = O.current - b;\n            }\n            St.current = new Date().getTime();\n        })() : (()=>{\n            O.current !== 1 / 0 && (gt.current = new Date().getTime(), r = setTimeout(()=>{\n                var b;\n                (b = t.onAutoClose) == null || b.call(t, t), $();\n            }, O.current));\n        })(), ()=>clearTimeout(r);\n    }, [\n        D,\n        u,\n        t,\n        N,\n        it,\n        Et,\n        $\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        t.delete && $();\n    }, [\n        $,\n        t.delete\n    ]);\n    function Zt() {\n        var r, m, c;\n        return P != null && P.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (r = t == null ? void 0 : t.classNames) == null ? void 0 : r.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, P.loading) : rt ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (m = t == null ? void 0 : t.classNames) == null ? void 0 : m.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, rt) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Yt, {\n            className: M(s == null ? void 0 : s.loader, (c = t == null ? void 0 : t.classNames) == null ? void 0 : c.loader),\n            visible: N === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: k,\n        className: M(ot, Kt, s == null ? void 0 : s.toast, (Dt = t == null ? void 0 : t.classNames) == null ? void 0 : Dt.toast, s == null ? void 0 : s.default, s == null ? void 0 : s[N], (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt[N]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (Nt = t.richColors) != null ? Nt : F,\n        \"data-styled\": !(t.jsx || t.unstyled || a),\n        \"data-mounted\": W,\n        \"data-promise\": !!t.promise,\n        \"data-swiped\": h,\n        \"data-removed\": A,\n        \"data-visible\": Ut,\n        \"data-y-position\": Gt,\n        \"data-x-position\": Qt,\n        \"data-index\": g,\n        \"data-front\": Vt,\n        \"data-swiping\": L,\n        \"data-dismissible\": V,\n        \"data-type\": N,\n        \"data-invert\": qt,\n        \"data-swipe-out\": ct,\n        \"data-swipe-direction\": lt,\n        \"data-expanded\": !!(D || B && W),\n        style: {\n            \"--index\": g,\n            \"--toasts-before\": g,\n            \"--z-index\": i.length - g,\n            \"--offset\": `${A ? R : U.current}px`,\n            \"--initial-height\": B ? \"auto\" : `${p}px`,\n            ...ut,\n            ...t.style\n        },\n        onDragEnd: ()=>{\n            z(!1), C(null), K.current = null;\n        },\n        onPointerDown: (r)=>{\n            ht || !V || (G.current = new Date, j(U.current), r.target.setPointerCapture(r.pointerId), r.target.tagName !== \"BUTTON\" && (z(!0), K.current = {\n                x: r.clientX,\n                y: r.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var x, Q, q, Z;\n            if (ct || !V) return;\n            K.current = null;\n            let r = Number(((x = k.current) == null ? void 0 : x.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0), m = Number(((Q = k.current) == null ? void 0 : Q.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0), c = new Date().getTime() - ((q = G.current) == null ? void 0 : q.getTime()), b = Y === \"x\" ? r : m, I = Math.abs(b) / c;\n            if (Math.abs(b) >= ye || I > .11) {\n                j(U.current), (Z = t.onDismiss) == null || Z.call(t, t), J(Y === \"x\" ? r > 0 ? \"right\" : \"left\" : m > 0 ? \"down\" : \"up\"), $(), d(!0), y(!1);\n                return;\n            }\n            z(!1), C(null);\n        },\n        onPointerMove: (r)=>{\n            var Q, q, Z, zt;\n            if (!K.current || !V || ((Q = window.getSelection()) == null ? void 0 : Q.toString().length) > 0) return;\n            let c = r.clientY - K.current.y, b = r.clientX - K.current.x, I = (q = n.swipeDirections) != null ? q : xe(st);\n            !Y && (Math.abs(b) > 1 || Math.abs(c) > 1) && C(Math.abs(b) > Math.abs(c) ? \"x\" : \"y\");\n            let x = {\n                x: 0,\n                y: 0\n            };\n            Y === \"y\" ? (I.includes(\"top\") || I.includes(\"bottom\")) && (I.includes(\"top\") && c < 0 || I.includes(\"bottom\") && c > 0) && (x.y = c) : Y === \"x\" && (I.includes(\"left\") || I.includes(\"right\")) && (I.includes(\"left\") && b < 0 || I.includes(\"right\") && b > 0) && (x.x = b), (Math.abs(x.x) > 0 || Math.abs(x.y) > 0) && y(!0), (Z = k.current) == null || Z.style.setProperty(\"--swipe-amount-x\", `${x.x}px`), (zt = k.current) == null || zt.style.setProperty(\"--swipe-amount-y\", `${x.y}px`);\n        }\n    }, Jt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": nt,\n        \"data-disabled\": ht,\n        \"data-close-button\": !0,\n        onClick: ht || !V ? ()=>{} : ()=>{\n            var r;\n            $(), (r = t.onDismiss) == null || r.call(t, t);\n        },\n        className: M(s == null ? void 0 : s.closeButton, (Bt = t == null ? void 0 : t.classNames) == null ? void 0 : Bt.closeButton)\n    }, (Ct = P == null ? void 0 : P.close) != null ? Ct : Ot) : null, t.jsx || /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.title) ? t.jsx ? t.jsx : typeof t.title == \"function\" ? t.title() : t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, N || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: M(s == null ? void 0 : s.icon, (kt = t == null ? void 0 : t.classNames) == null ? void 0 : kt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Zt() : null, t.type !== \"loading\" ? t.icon || (P == null ? void 0 : P[N]) || jt(N) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: M(s == null ? void 0 : s.content, (It = t == null ? void 0 : t.classNames) == null ? void 0 : It.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: M(s == null ? void 0 : s.title, (Mt = t == null ? void 0 : t.classNames) == null ? void 0 : Mt.title)\n    }, typeof t.title == \"function\" ? t.title() : t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: M(at, Xt, s == null ? void 0 : s.description, (Ht = t == null ? void 0 : t.classNames) == null ? void 0 : Ht.description)\n    }, typeof t.description == \"function\" ? t.description() : t.description) : null), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.cancel) ? t.cancel : t.cancel && tt(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || ft,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.cancel) && V && ((c = (m = t.cancel).onClick) == null || c.call(m, r), $());\n        },\n        className: M(s == null ? void 0 : s.cancelButton, (At = t == null ? void 0 : t.classNames) == null ? void 0 : At.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.action) ? t.action : t.action && tt(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || l,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.action) && ((c = (m = t.action).onClick) == null || c.call(m, r), !r.defaultPrevented && $());\n        },\n        className: M(s == null ? void 0 : s.actionButton, (Lt = t == null ? void 0 : t.classNames) == null ? void 0 : Lt.actionButton)\n    }, t.action.label) : null));\n};\nfunction _t() {\n    if (true) return \"ltr\";\n    let n = document.documentElement.getAttribute(\"dir\");\n    return n === \"auto\" || !n ? window.getComputedStyle(document.documentElement).direction : n;\n}\nfunction Te(n, e) {\n    let t = {};\n    return [\n        n,\n        e\n    ].forEach((a, u)=>{\n        let f = u === 1, w = f ? \"--mobile-offset\" : \"--offset\", S = f ? ge : me;\n        function g(i) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((D)=>{\n                t[`${w}-${D}`] = typeof i == \"number\" ? `${i}px` : i;\n            });\n        }\n        typeof a == \"number\" || typeof a == \"string\" ? g(a) : typeof a == \"object\" ? [\n            \"top\",\n            \"right\",\n            \"bottom\",\n            \"left\"\n        ].forEach((i)=>{\n            a[i] === void 0 ? t[`${w}-${i}`] = S : t[`${w}-${i}`] = typeof a[i] == \"number\" ? `${a[i]}px` : a[i];\n        }) : g(S);\n    }), t;\n}\nfunction Oe() {\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((t)=>{\n            if (t.dismiss) {\n                setTimeout(()=>{\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                        e((a)=>a.filter((u)=>u.id !== t.id));\n                    });\n                });\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    e((a)=>{\n                        let u = a.findIndex((f)=>f.id === t.id);\n                        return u !== -1 ? [\n                            ...a.slice(0, u),\n                            {\n                                ...a[u],\n                                ...t\n                            },\n                            ...a.slice(u + 1)\n                        ] : [\n                            t,\n                            ...a\n                        ];\n                    });\n                });\n            });\n        }), []), {\n        toasts: n\n    };\n}\nvar $e = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(e, t) {\n    let { invert: a, position: u = \"bottom-right\", hotkey: f = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: w, closeButton: S, className: g, offset: i, mobileOffset: D, theme: T = \"light\", richColors: F, duration: et, style: ut, visibleToasts: ft = pe, toastOptions: l, dir: ot = _t(), gap: at = be, loadingIcon: X, icons: st, containerAriaLabel: pt = \"Notifications\", pauseWhenPageIsHidden: rt } = e, [B, s] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), P = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Array.from(new Set([\n            u\n        ].concat(B.filter((d)=>d.position).map((d)=>d.position)))), [\n        B,\n        u\n    ]), [nt, it] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(T !== \"system\" ? T :  false ? 0 : \"light\"), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), mt = f.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), L = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), z = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), ct = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((d)=>{\n        s((h)=>{\n            var y;\n            return (y = h.find((R)=>R.id === d.id)) != null && y.delete || v.dismiss(d.id), h.filter(({ id: R })=>R !== d.id);\n        });\n    }, []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((d)=>{\n            if (d.dismiss) {\n                s((h)=>h.map((y)=>y.id === d.id ? {\n                            ...y,\n                            delete: !0\n                        } : y));\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    s((h)=>{\n                        let y = h.findIndex((R)=>R.id === d.id);\n                        return y !== -1 ? [\n                            ...h.slice(0, y),\n                            {\n                                ...h[y],\n                                ...d\n                            },\n                            ...h.slice(y + 1)\n                        ] : [\n                            d,\n                            ...h\n                        ];\n                    });\n                });\n            });\n        }), []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (T !== \"system\") {\n            H(T);\n            return;\n        }\n        if (T === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? H(\"dark\") : H(\"light\")), \"undefined\" == \"undefined\") return;\n        let d = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        try {\n            d.addEventListener(\"change\", ({ matches: h })=>{\n                H(h ? \"dark\" : \"light\");\n            });\n        } catch (h) {\n            d.addListener(({ matches: y })=>{\n                try {\n                    H(y ? \"dark\" : \"light\");\n                } catch (R) {\n                    console.error(R);\n                }\n            });\n        }\n    }, [\n        T\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        B.length <= 1 && C(!1);\n    }, [\n        B\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let d = (h)=>{\n            var R, j;\n            f.every((p)=>h[p] || h.code === p) && (C(!0), (R = A.current) == null || R.focus()), h.code === \"Escape\" && (document.activeElement === A.current || (j = A.current) != null && j.contains(document.activeElement)) && C(!1);\n        };\n        return document.addEventListener(\"keydown\", d), ()=>document.removeEventListener(\"keydown\", d);\n    }, [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (A.current) return ()=>{\n            L.current && (L.current.focus({\n                preventScroll: !0\n            }), L.current = null, z.current = !1);\n        };\n    }, [\n        A.current\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: t,\n        \"aria-label\": `${pt} ${mt}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: !0\n    }, P.map((d, h)=>{\n        var j;\n        let [y, R] = d.split(\"-\");\n        return B.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: d,\n            dir: ot === \"auto\" ? _t() : ot,\n            tabIndex: -1,\n            ref: A,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": W,\n            \"data-y-position\": y,\n            \"data-lifted\": Y && B.length > 1 && !w,\n            \"data-x-position\": R,\n            style: {\n                \"--front-toast-height\": `${((j = nt[0]) == null ? void 0 : j.height) || 0}px`,\n                \"--width\": `${he}px`,\n                \"--gap\": `${at}px`,\n                ...ut,\n                ...Te(i, D)\n            },\n            onBlur: (p)=>{\n                z.current && !p.currentTarget.contains(p.relatedTarget) && (z.current = !1, L.current && (L.current.focus({\n                    preventScroll: !0\n                }), L.current = null));\n            },\n            onFocus: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || z.current || (z.current = !0, L.current = p.relatedTarget);\n            },\n            onMouseEnter: ()=>C(!0),\n            onMouseMove: ()=>C(!0),\n            onMouseLeave: ()=>{\n                lt || C(!1);\n            },\n            onDragEnd: ()=>C(!1),\n            onPointerDown: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || J(!0);\n            },\n            onPointerUp: ()=>J(!1)\n        }, B.filter((p)=>!p.position && h === 0 || p.position === d).map((p, _)=>{\n            var O, G;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ve, {\n                key: p.id,\n                icons: st,\n                index: _,\n                toast: p,\n                defaultRichColors: F,\n                duration: (O = l == null ? void 0 : l.duration) != null ? O : et,\n                className: l == null ? void 0 : l.className,\n                descriptionClassName: l == null ? void 0 : l.descriptionClassName,\n                invert: a,\n                visibleToasts: ft,\n                closeButton: (G = l == null ? void 0 : l.closeButton) != null ? G : S,\n                interacting: lt,\n                position: d,\n                style: l == null ? void 0 : l.style,\n                unstyled: l == null ? void 0 : l.unstyled,\n                classNames: l == null ? void 0 : l.classNames,\n                cancelButtonStyle: l == null ? void 0 : l.cancelButtonStyle,\n                actionButtonStyle: l == null ? void 0 : l.actionButtonStyle,\n                removeToast: ct,\n                toasts: B.filter((k)=>k.position == p.position),\n                heights: nt.filter((k)=>k.position == p.position),\n                setHeights: it,\n                expandByDefault: w,\n                gap: at,\n                loadingIcon: X,\n                expanded: Y,\n                pauseWhenPageIsHidden: rt,\n                swipeDirections: e.swipeDirections\n            });\n        })) : null;\n    }));\n});\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0),
/* harmony export */   toast: () => (/* binding */ e1),
/* harmony export */   useSonner: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\node_modules\sonner\dist\index.mjs#Toaster`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\node_modules\sonner\dist\index.mjs#toast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\node_modules\sonner\dist\index.mjs#useSonner`);


/***/ })

};
;