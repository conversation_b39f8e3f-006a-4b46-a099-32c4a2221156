'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { 
  Download,
  CheckCircle, 
  XCircle, 
  Loader2,
  Clock,
  Zap,
  Info,
  Square,
  AlertCircle
} from 'lucide-react';
import { scrapingService, ScrapeRequest, ScrapeResponse } from '@/services/scrapingService';
import ScrapingProgress from '@/components/scrape/ScrapingProgress';
import {
  persistJob,
  getActiveJobs,
  migrateLegacyJob,
  cleanupJobs
} from '@/lib/jobPersistence';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { toast } from 'sonner';

// Types
interface ScrapingState {
  status: 'idle' | 'testing' | 'scraping' | 'completed' | 'error';
  result: ScrapeResponse | null;
  error: string | null;
  jobId: string | null;
}

type ScrapingMode = 'traditional' | 'realtime';

/**
 * Main scraping page content component
 */
function ScrapePageContent() {
  const router = useRouter();
  const [url, setUrl] = useState('');
  const [maxPages, setMaxPages] = useState(10);
  const [scrapingMode, setScrapingMode] = useState<ScrapingMode>('traditional');
  const [scrapingState, setScrapingState] = useState<ScrapingState>({
    status: 'idle',
    result: null,
    error: null,
    jobId: null
  });
  
  // Real-time progress modal state
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [activeJobId, setActiveJobId] = useState<string | null>(null);

  const validateUrl = (url: string): boolean => {
    try {
      return url.includes('webtruyen.diendantruyen.com');
    } catch (error) {
      return false;
    }
  };

  const handleStartScraping = async () => {
    try {
      // Validation
      if (!url.trim()) {
        toast.error('Please enter a story URL');
        return;
      }
      if (!validateUrl(url)) {
        toast.error('Please enter a valid webtruyen.diendantruyen.com URL');
        return;
      }
      if (maxPages < 1 || maxPages > 100) {
        toast.error('Maximum pages must be between 1 and 100');
        return;
      }

      setScrapingState({
        status: 'scraping',
        result: null,
        error: null,
        jobId: null
      });

      const request: ScrapeRequest = {
        story_url: url.trim(),
        max_pages: maxPages
      };

      if (scrapingMode === 'realtime') {
        // Start real-time scraping
        const response = await scrapingService.scrapeStoryAsync(request);
        
        // Persist the job for recovery
        persistJob({
          jobId: response.job_id,
          url: url.trim(),
          maxPages,
          status: 'active',
          timestamp: Date.now()
        });

        if (activeJobId !== response.job_id) {
          setActiveJobId(response.job_id);
          setShowProgressModal(true);
        }

        toast.success('Real-time scraping started!');
      } else {
        // Traditional scraping
        const response = await scrapingService.scrapeStory(request);
        
        setScrapingState({
          status: 'completed',
          result: response,
          error: null,
          jobId: null
        });
        
        toast.success('Story scraped successfully!');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setScrapingState({
        status: 'error',
        result: null,
        error: errorMessage,
        jobId: null
      });
      toast.error(`Scraping failed: ${errorMessage}`);
    }
  };

  const handleReset = () => {
    setScrapingState({
      status: 'idle',
      result: null,
      error: null,
      jobId: null
    });
    setUrl('');
    setMaxPages(10);
    setActiveJobId(null);
    setShowProgressModal(false);
  };

  const handleViewStory = () => {
    try {
      if (!scrapingState.result?.story_id) {
        toast.error('No story ID available');
        return;
      }
      router.push(`/stories/${scrapingState.result.story_id}`);
    } catch (error) {
      toast.error('Failed to navigate to story');
    }
  };

  // Initialize jobs and handle modal state
  useEffect(() => {
    const initializeJobs = async () => {
      try {
        // Migrate legacy job storage
        migrateLegacyJob();
        
        // Clean up expired jobs
        cleanupJobs();
        
        // Check for active jobs and show notifications
        const activeJobs = getActiveJobs();
        if (activeJobs.length > 0 && activeJobs.length === 1) {
          const job = activeJobs[0];
          if (activeJobId !== job.jobId) {
            setActiveJobId(job.jobId);
            setUrl(job.url);
            setMaxPages(job.maxPages);
            setShowProgressModal(true);
          }
        }
      } catch (error) {
        toast.error('Failed to recover previous scraping jobs');
      }
    };
    
    initializeJobs();
  }, [activeJobId]);

  // Handle modal state based on activeJobId
  useEffect(() => {
    setShowProgressModal(!!activeJobId);
    
    // Cleanup function to persist job on unmount
    return () => {
      if (activeJobId) {
        persistJob({
          jobId: activeJobId,
          url: url,
          maxPages: maxPages,
          status: 'active',
          timestamp: Date.now()
        });
      }
    };
  }, [activeJobId, url, maxPages]);

  const getStatusIcon = () => {
    switch (scrapingState.status) {
      case 'scraping':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Download className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Story Scraper
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                Extract stories and chapters from webtruyen.diendantruyen.com with real-time progress tracking
              </p>
            </div>
          </div>

          {/* Main Scraping Card */}
          <Card className="w-full border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 bg-card/50 backdrop-blur-sm">
            <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-t-lg">
              <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                {getStatusIcon()}
                Scrape New Story
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground/80">
                Enter a story URL and start scraping
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-5">
              {/* Scraping Mode */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Scraping Mode</Label>
                <Tabs value={scrapingMode} onValueChange={(value) => setScrapingMode(value as ScrapingMode)}>
                  <TabsList className="grid w-full grid-cols-2 h-10 bg-muted/50 backdrop-blur-sm">
                    <TabsTrigger value="traditional" className="text-xs font-medium transition-all duration-200 hover:bg-background/80">
                      Traditional
                    </TabsTrigger>
                    <TabsTrigger value="realtime" className="text-xs font-medium transition-all duration-200 hover:bg-background/80">
                      Real-time
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="traditional" className="mt-3">
                    <p className="text-sm text-muted-foreground">
                      Complete scraping process with results displayed on this page. 
                      You&apos;ll wait for the entire process to finish before seeing results.
                    </p>
                  </TabsContent>
                  <TabsContent value="realtime" className="mt-3">
                    <p className="text-sm text-muted-foreground">
                      Start scraping immediately with real-time progress updates in a modal. 
                      You can navigate away and return to check progress anytime.
                    </p>
                  </TabsContent>
                </Tabs>
              </div>

              <Separator />

              {/* Input Fields */}
              <div className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="url" className="text-sm font-medium flex items-center gap-2">
                    Story URL
                    <span className="text-red-500">*</span>
                  </Label>
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="url"
                        type="url"
                        placeholder="https://webtruyen.diendantruyen.com/truyen/..."
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        disabled={scrapingState.status === 'scraping'}
                        className={`h-11 transition-all duration-300 focus:ring-2 focus:ring-primary/20 hover:border-primary/50 ${
                          url && !validateUrl(url) 
                            ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' 
                            : url && validateUrl(url)
                            ? 'border-green-500 focus:ring-green-500/20 focus:border-green-500'
                            : ''
                        }`}
                      />
                      {url && validateUrl(url) && (
                        <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                      )}
                      {url && !validateUrl(url) && (
                        <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
                      )}
                    </div>
                    {url && !validateUrl(url) && (
                      <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200">
                        <AlertCircle className="h-4 w-4 flex-shrink-0" />
                        <span>Please enter a valid webtruyen.diendantruyen.com URL</span>
                      </div>
                    )}
                    {url && validateUrl(url) && (
                      <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded-md border border-green-200">
                        <CheckCircle className="h-4 w-4 flex-shrink-0" />
                        <span>Valid URL detected</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="maxPages" className="text-sm font-medium">
                    Maximum Pages
                  </Label>
                  <div className="space-y-2">
                    <Input
                      id="maxPages"
                      type="number"
                      min="1"
                      max="100"
                      value={maxPages}
                      onChange={(e) => setMaxPages(parseInt(e.target.value) || 10)}
                      disabled={scrapingState.status === 'scraping'}
                      className="h-11 transition-all duration-300 focus:ring-2 focus:ring-primary/20 hover:border-primary/50"
                    />
                    <p className="text-sm text-muted-foreground bg-muted/30 p-2 rounded-md">
                      📄 Number of pages to scrape (1-100). Each page typically contains 10-20 chapters.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={handleStartScraping}
                  disabled={scrapingState.status === 'scraping' || !url || !validateUrl(url)}
                  className={`flex-1 h-12 font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] ${
                    scrapingState.status === 'scraping'
                      ? 'bg-blue-500 hover:bg-blue-600 cursor-not-allowed'
                      : 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70'
                  }`}
                  size="lg"
                >
                  {scrapingState.status === 'scraping' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span className="animate-pulse">
                        {scrapingMode === 'realtime' ? 'Starting...' : 'Scraping...'}
                      </span>
                    </>
                  ) : (
                    <>
                      {scrapingMode === 'realtime' ? (
                        <>
                          <Zap className="mr-2 h-4 w-4" />
                          Start Real-time Scraping
                        </>
                      ) : (
                        <>
                          <Download className="mr-2 h-4 w-4" />
                          Start Traditional Scraping
                        </>
                      )}
                    </>
                  )}
                </Button>
                
                {scrapingState.status !== 'idle' && (
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    disabled={scrapingState.status === 'scraping'}
                    size="lg"
                    className={`flex-1 sm:flex-none h-12 font-medium transition-all duration-300 hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] ${
                      scrapingState.status === 'scraping'
                        ? 'opacity-50 cursor-not-allowed'
                        : 'border-border/50 hover:border-primary/50 hover:bg-primary/5'
                    }`}
                  >
                    <Square className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                )}
              </div>

              {/* Progress Display */}
              {scrapingState.status !== 'idle' && (
                <div className="space-y-4">
                  <Separator />
                  
                  <ScrapingProgress 
                    status={scrapingState.status}
                    result={scrapingState.result}
                    error={scrapingState.error}
                    onViewStory={handleViewStory}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* How it works */}
          <Card className="border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 bg-card/30 backdrop-blur-sm">
            <CardHeader className="pb-4 bg-gradient-to-r from-muted/20 to-muted/10 rounded-t-lg">
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <Info className="h-5 w-5 text-primary" />
                How it works
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground mt-2">
                Our scraping process follows these simple steps to extract your story
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-1">
              <div className="grid gap-4">
                <div className="flex items-start gap-4 group hover:bg-muted/30 p-4 rounded-xl transition-all duration-300 hover:shadow-md border border-transparent hover:border-primary/20">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-white flex items-center justify-center text-sm font-bold shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                    1
                  </div>
                  <div className="space-y-2 flex-1">
                    <h3 className="font-bold text-foreground text-base flex items-center gap-2">
                      📖 Extract Story Information
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Scrapes basic story metadata including title, author, description, and cover image from the main story page.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4 group hover:bg-muted/30 p-4 rounded-xl transition-all duration-300 hover:shadow-md border border-transparent hover:border-primary/20">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 text-white flex items-center justify-center text-sm font-bold shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                    2
                  </div>
                  <div className="space-y-2 flex-1">
                    <h3 className="font-bold text-foreground text-base flex items-center gap-2">
                      🔍 Discover Chapter Pages
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Finds all chapter listing pages up to the specified maximum, ensuring comprehensive coverage.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4 group hover:bg-muted/30 p-4 rounded-xl transition-all duration-300 hover:shadow-md border border-transparent hover:border-primary/20">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-orange-500 to-orange-600 text-white flex items-center justify-center text-sm font-bold shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                    3
                  </div>
                  <div className="space-y-2 flex-1">
                    <h3 className="font-bold text-foreground text-base flex items-center gap-2">
                      📝 Extract Chapter URLs
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Collects all chapter URLs and metadata from each page, organizing them for easy access.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4 group hover:bg-muted/30 p-4 rounded-xl transition-all duration-300 hover:shadow-md border border-transparent hover:border-primary/20">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-500 to-green-600 text-white flex items-center justify-center text-sm font-bold shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                    4
                  </div>
                  <div className="space-y-2 flex-1">
                    <h3 className="font-bold text-foreground text-base flex items-center gap-2">
                      💾 Store in Database
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Saves the complete story structure for future content scraping and enhancement, ready for reading.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg border border-primary/20">
                <p className="text-sm text-muted-foreground text-center">
                  ⚡ <strong>Pro tip:</strong> Use real-time mode to track progress and navigate away while scraping continues in the background.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

/**
 * ScrapePage component with error boundary
 */
export default function ScrapePage() {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <Card className="w-full">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <XCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div className="space-y-2">
                  <h2 className="text-xl font-semibold">Something went wrong</h2>
                  <p className="text-muted-foreground">
                    {error?.message || 'An unexpected error occurred'}
                  </p>
                </div>
                <div className="flex gap-2 justify-center">
                  <Button onClick={resetError} variant="outline">
                    Try Again
                  </Button>
                  <Button onClick={() => window.location.reload()}>
                    Reload Page
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      onError={(error) => {
        console.error('Scrape page error:', error);
      }}
    >
      <ScrapePageContent />
    </ErrorBoundary>
  );
}