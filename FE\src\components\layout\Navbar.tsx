'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { EnhancedHamburger } from '@/components/ui/EnhancedUI';
import { motion, AnimatePresence } from 'framer-motion';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isOpen && !target.closest('#mobile-menu') && !target.closest('[aria-controls="mobile-menu"]')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  const navLinks = [
    { href: '/', label: 'Dashboard' },
    { href: '/scrape', label: 'Scrape' },
  ];

  return (
    <nav className="bg-zinc-800/30 border-b border-gray-700 backdrop-blur-2xl sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-mobile-padding sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14 sm:h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link 
              href="/" 
              className="text-white font-bold text-lg sm:text-xl hover:text-gray-200 transition-colors touch-manipulation"
            >
              WebTruyen
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-1">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    isActiveLink(link.href)
                      ? 'bg-zinc-700 text-white'
                      : 'text-gray-300 hover:bg-zinc-700/50 hover:text-white'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <EnhancedHamburger
              isOpen={isOpen}
              onToggle={() => setIsOpen(!isOpen)}
              size="md"
              variant="default"
            />
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="md:hidden overflow-hidden"
            id="mobile-menu"
          >
            <motion.div 
              className="px-mobile-padding pt-2 pb-3 space-y-1 bg-zinc-800/50 backdrop-blur-sm border-t border-gray-700/50"
              initial={{ y: -20 }}
              animate={{ y: 0 }}
              transition={{ delay: 0.1, duration: 0.2 }}
            >
              {navLinks.map((link, index) => (
                <motion.div
                  key={link.href}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 + index * 0.1, duration: 0.2 }}
                >
                  <Link
                    href={link.href}
                    className={`block px-3 py-3 rounded-md text-base font-medium transition-all duration-200 touch-manipulation min-h-touch-target flex items-center ${
                      isActiveLink(link.href)
                        ? 'bg-zinc-700 text-white'
                        : 'text-gray-300 hover:bg-zinc-700/50 hover:text-white active:bg-zinc-700'
                    }`}
                  >
                    {link.label}
                  </Link>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;